using UnityEngine;

namespace TowerDefense.Core
{
    public class BuildZone : MonoBehaviour
    {
        [Header("Build Zone Settings")]
        [SerializeField] private bool allowBuilding = true;
        [SerializeField] private Material highlightMaterial;
        [SerializeField] private Material normalMaterial;
        [SerializeField] private bool showBuildIndicator = true;

        [Header("Restrictions")]
        [SerializeField] private Towers.TowerType[] allowedTowerTypes;
        [SerializeField] private bool restrictTowerTypes = false;

        private Renderer zoneRenderer;
        private bool isHighlighted = false;
        private bool isOccupied = false;
        private GameObject occupyingTower;

        private void Start()
        {
            SetupBuildZone();
        }

        private void SetupBuildZone()
        {
            zoneRenderer = GetComponent<Renderer>();
            
            // Ensure we have a collider for detection
            Collider collider = GetComponent<Collider>();
            if (collider == null)
            {
                BoxCollider boxCollider = gameObject.AddComponent<BoxCollider>();
                boxCollider.isTrigger = true;
            }
            else
            {
                collider.isTrigger = true;
            }

            // Set initial material
            if (zoneRenderer != null && normalMaterial != null)
            {
                zoneRenderer.material = normalMaterial;
            }

            // Hide renderer if not showing build indicator
            if (zoneRenderer != null && !showBuildIndicator)
            {
                zoneRenderer.enabled = false;
            }
        }

        public bool CanBuildTower(Towers.TowerType towerType)
        {
            if (!allowBuilding || isOccupied)
                return false;

            if (restrictTowerTypes && allowedTowerTypes != null)
            {
                bool typeAllowed = false;
                foreach (Towers.TowerType allowedType in allowedTowerTypes)
                {
                    if (allowedType == towerType)
                    {
                        typeAllowed = true;
                        break;
                    }
                }
                return typeAllowed;
            }

            return true;
        }

        public bool CanBuildTower()
        {
            return allowBuilding && !isOccupied;
        }

        public void SetOccupied(GameObject tower)
        {
            isOccupied = true;
            occupyingTower = tower;
        }

        public void SetUnoccupied()
        {
            isOccupied = false;
            occupyingTower = null;
        }

        public void HighlightZone(bool highlight)
        {
            if (isHighlighted == highlight || zoneRenderer == null)
                return;

            isHighlighted = highlight;

            if (highlight && highlightMaterial != null)
            {
                zoneRenderer.material = highlightMaterial;
                zoneRenderer.enabled = true;
            }
            else if (normalMaterial != null)
            {
                zoneRenderer.material = normalMaterial;
                zoneRenderer.enabled = showBuildIndicator;
            }
        }

        public Vector3 GetBuildPosition()
        {
            return transform.position;
        }

        public bool IsOccupied()
        {
            return isOccupied;
        }

        public GameObject GetOccupyingTower()
        {
            return occupyingTower;
        }

        public void SetAllowBuilding(bool allow)
        {
            allowBuilding = allow;
        }

        public void SetAllowedTowerTypes(Towers.TowerType[] types)
        {
            allowedTowerTypes = types;
            restrictTowerTypes = types != null && types.Length > 0;
        }

        private void OnTriggerEnter(Collider other)
        {
            // Check if a tower entered this build zone
            Towers.Tower tower = other.GetComponent<Towers.Tower>();
            if (tower != null && !isOccupied)
            {
                SetOccupied(other.gameObject);
            }
        }

        private void OnTriggerExit(Collider other)
        {
            // Check if the occupying tower left this build zone
            if (other.gameObject == occupyingTower)
            {
                SetUnoccupied();
            }
        }

        private void OnDrawGizmos()
        {
            // Draw build zone bounds in scene view
            Gizmos.color = allowBuilding ? Color.green : Color.red;
            if (isOccupied)
                Gizmos.color = Color.yellow;

            Gizmos.DrawWireCube(transform.position, transform.localScale);
        }

        private void OnDrawGizmosSelected()
        {
            // Draw more detailed info when selected
            Gizmos.color = Color.cyan;
            Gizmos.DrawCube(transform.position, transform.localScale);
        }
    }
}
