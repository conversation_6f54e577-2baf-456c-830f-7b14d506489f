using UnityEngine;

namespace TowerDefense.Towers
{
    public class Projectile : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [SerializeField] private float speed = 10f;
        [SerializeField] private float lifeTime = 5f;
        [SerializeField] private bool trackTarget = true;
        [SerializeField] private float explosionRadius = 0f;

        [Header("Visual Effects")]
        [SerializeField] private GameObject hitEffect;
        [SerializeField] private GameObject explosionEffect;
        [SerializeField] private TrailRenderer trail;

        [Header("Audio")]
        [SerializeField] private AudioClip hitSound;
        [SerializeField] private AudioClip explosionSound;

        private Enemy targetEnemy;
        private int damage;
        private Vector3 targetPosition;
        private bool hasHit = false;
        private AudioSource audioSource;

        // Events
        public System.Action<Enemy, int> OnHit;

        private void Start()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            // Destroy projectile after lifetime
            Destroy(gameObject, lifeTime);
        }

        private void Update()
        {
            if (hasHit)
                return;

            MoveProjectile();
            CheckForHit();
        }

        public void Initialize(Enemy target, int projectileDamage)
        {
            targetEnemy = target;
            damage = projectileDamage;
            
            if (target != null)
            {
                targetPosition = target.transform.position;
            }
        }

        public void Initialize(Vector3 position, int projectileDamage)
        {
            targetPosition = position;
            damage = projectileDamage;
            targetEnemy = null;
        }

        private void MoveProjectile()
        {
            Vector3 direction;

            if (trackTarget && targetEnemy != null)
            {
                // Track moving target
                targetPosition = targetEnemy.transform.position;
                direction = (targetPosition - transform.position).normalized;
            }
            else
            {
                // Move to fixed position
                direction = (targetPosition - transform.position).normalized;
            }

            transform.position += direction * speed * Time.deltaTime;
            transform.LookAt(targetPosition);
        }

        private void CheckForHit()
        {
            if (targetEnemy != null)
            {
                float distanceToTarget = Vector3.Distance(transform.position, targetEnemy.transform.position);
                if (distanceToTarget < 0.5f)
                {
                    HitTarget();
                }
            }
            else
            {
                float distanceToPosition = Vector3.Distance(transform.position, targetPosition);
                if (distanceToPosition < 0.5f)
                {
                    HitPosition();
                }
            }
        }

        private void HitTarget()
        {
            if (hasHit || targetEnemy == null)
                return;

            hasHit = true;

            // Deal damage to target
            targetEnemy.TakeDamage(damage);
            OnHit?.Invoke(targetEnemy, damage);

            // Handle explosion damage
            if (explosionRadius > 0f)
            {
                DealExplosionDamage();
            }

            // Play effects
            PlayHitEffects();

            // Destroy projectile
            DestroyProjectile();
        }

        private void HitPosition()
        {
            if (hasHit)
                return;

            hasHit = true;

            // Handle explosion damage at position
            if (explosionRadius > 0f)
            {
                DealExplosionDamage();
            }

            // Play effects
            PlayHitEffects();

            // Destroy projectile
            DestroyProjectile();
        }

        private void DealExplosionDamage()
        {
            Collider[] colliders = Physics.OverlapSphere(transform.position, explosionRadius);
            
            foreach (Collider collider in colliders)
            {
                Enemy enemy = collider.GetComponent<Enemy>();
                if (enemy != null)
                {
                    // Calculate damage based on distance (optional)
                    float distance = Vector3.Distance(transform.position, enemy.transform.position);
                    float damageMultiplier = 1f - (distance / explosionRadius);
                    int explosionDamage = Mathf.RoundToInt(damage * damageMultiplier);
                    
                    enemy.TakeDamage(explosionDamage);
                    OnHit?.Invoke(enemy, explosionDamage);
                }
            }

            // Play explosion sound
            if (audioSource != null && explosionSound != null)
            {
                audioSource.PlayOneShot(explosionSound);
            }

            // Spawn explosion effect
            if (explosionEffect != null)
            {
                GameObject effect = Instantiate(explosionEffect, transform.position, Quaternion.identity);
                Destroy(effect, 2f);
            }
        }

        private void PlayHitEffects()
        {
            // Play hit sound
            if (audioSource != null && hitSound != null)
            {
                audioSource.PlayOneShot(hitSound);
            }

            // Spawn hit effect
            if (hitEffect != null)
            {
                GameObject effect = Instantiate(hitEffect, transform.position, Quaternion.identity);
                Destroy(effect, 1f);
            }
        }

        private void DestroyProjectile()
        {
            // Disable trail if it exists
            if (trail != null)
            {
                trail.enabled = false;
            }

            // If we have audio playing, wait for it to finish
            if (audioSource != null && audioSource.isPlaying)
            {
                // Hide visual components but keep audio playing
                GetComponent<Renderer>().enabled = false;
                GetComponent<Collider>().enabled = false;
                
                // Destroy after audio finishes
                Destroy(gameObject, audioSource.clip.length);
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            if (hasHit)
                return;

            Enemy enemy = other.GetComponent<Enemy>();
            if (enemy != null && (targetEnemy == null || enemy == targetEnemy))
            {
                targetEnemy = enemy;
                HitTarget();
            }
        }

        private void OnDrawGizmosSelected()
        {
            if (explosionRadius > 0f)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(transform.position, explosionRadius);
            }
        }
    }
}
