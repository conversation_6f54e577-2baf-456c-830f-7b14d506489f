using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using TMPro;

namespace TowerDefense.UI
{
    public class MainMenu : MonoBehaviour
    {
        [Header("Main Menu Panels")]
        [SerializeField] private GameObject mainPanel;
        [SerializeField] private GameObject levelSelectPanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject creditsPanel;

        [Header("Main Menu Buttons")]
        [SerializeField] private Button playButton;
        [SerializeField] private Button levelSelectButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button creditsButton;
        [SerializeField] private But<PERSON> quitButton;

        [Header("Level Selection")]
        [SerializeField] private Transform levelButtonContainer;
        [SerializeField] private GameObject levelButtonPrefab;
        [SerializeField] private Data.LevelData[] availableLevels;

        [Header("Settings")]
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Button backFromSettingsButton;

        [Header("Credits")]
        [SerializeField] private Button backFromCreditsButton;
        [SerializeField] private TextMeshProUGUI creditsText;

        [Header("Audio")]
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip backgroundMusic;

        private void Start()
        {
            InitializeMenu();
            SetupEventListeners();
            PlayBackgroundMusic();
        }

        private void InitializeMenu()
        {
            // Show main panel, hide others
            ShowPanel(mainPanel);
            
            // Initialize volume sliders
            if (Audio.AudioManager.Instance != null)
            {
                if (masterVolumeSlider != null)
                    masterVolumeSlider.value = Audio.AudioManager.Instance.GetMasterVolume();
                
                if (musicVolumeSlider != null)
                    musicVolumeSlider.value = Audio.AudioManager.Instance.GetMusicVolume();
                
                if (sfxVolumeSlider != null)
                    sfxVolumeSlider.value = Audio.AudioManager.Instance.GetSFXVolume();
            }

            // Create level selection buttons
            CreateLevelButtons();
        }

        private void SetupEventListeners()
        {
            // Main menu buttons
            if (playButton != null)
                playButton.onClick.AddListener(() => StartFirstLevel());
            
            if (levelSelectButton != null)
                levelSelectButton.onClick.AddListener(() => ShowPanel(levelSelectPanel));
            
            if (settingsButton != null)
                settingsButton.onClick.AddListener(() => ShowPanel(settingsPanel));
            
            if (creditsButton != null)
                creditsButton.onClick.AddListener(() => ShowPanel(creditsPanel));
            
            if (quitButton != null)
                quitButton.onClick.AddListener(QuitGame);

            // Settings
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(SetMasterVolume);
            
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(SetMusicVolume);
            
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(SetSFXVolume);
            
            if (backFromSettingsButton != null)
                backFromSettingsButton.onClick.AddListener(() => ShowPanel(mainPanel));

            // Credits
            if (backFromCreditsButton != null)
                backFromCreditsButton.onClick.AddListener(() => ShowPanel(mainPanel));
        }

        private void CreateLevelButtons()
        {
            if (levelButtonContainer == null || levelButtonPrefab == null || availableLevels == null)
                return;

            // Clear existing buttons
            foreach (Transform child in levelButtonContainer)
            {
                Destroy(child.gameObject);
            }

            // Create button for each level
            for (int i = 0; i < availableLevels.Length; i++)
            {
                Data.LevelData levelData = availableLevels[i];
                if (levelData == null) continue;

                GameObject buttonObject = Instantiate(levelButtonPrefab, levelButtonContainer);
                LevelButton levelButton = buttonObject.GetComponent<LevelButton>();
                
                if (levelButton != null)
                {
                    levelButton.Initialize(levelData, LoadLevel);
                }
                else
                {
                    // Fallback if no LevelButton component
                    Button button = buttonObject.GetComponent<Button>();
                    TextMeshProUGUI buttonText = buttonObject.GetComponentInChildren<TextMeshProUGUI>();
                    
                    if (button != null)
                    {
                        button.onClick.AddListener(() => LoadLevel(levelData));
                        button.interactable = levelData.IsUnlocked();
                    }
                    
                    if (buttonText != null)
                    {
                        buttonText.text = levelData.levelName;
                    }
                }
            }
        }

        private void ShowPanel(GameObject panelToShow)
        {
            // Hide all panels
            if (mainPanel != null) mainPanel.SetActive(false);
            if (levelSelectPanel != null) levelSelectPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
            if (creditsPanel != null) creditsPanel.SetActive(false);

            // Show requested panel
            if (panelToShow != null)
            {
                panelToShow.SetActive(true);
            }

            // Play button sound
            PlayButtonSound();
        }

        private void StartFirstLevel()
        {
            if (availableLevels != null && availableLevels.Length > 0)
            {
                LoadLevel(availableLevels[0]);
            }
            else
            {
                // Fallback to scene loading
                LoadScene("Level1");
            }
        }

        private void LoadLevel(Data.LevelData levelData)
        {
            if (levelData == null)
                return;

            if (!levelData.IsUnlocked())
            {
                Debug.Log("Level is locked!");
                return;
            }

            PlayButtonSound();
            
            // Load the level scene
            string sceneName = $"Level{levelData.levelNumber}";
            LoadScene(sceneName);
        }

        private void LoadScene(string sceneName)
        {
            if (Application.CanStreamedLevelBeLoaded(sceneName))
            {
                SceneManager.LoadScene(sceneName);
            }
            else
            {
                Debug.LogError($"Scene '{sceneName}' not found in build settings!");
            }
        }

        private void SetMasterVolume(float volume)
        {
            if (Audio.AudioManager.Instance != null)
            {
                Audio.AudioManager.Instance.SetMasterVolume(volume);
            }
        }

        private void SetMusicVolume(float volume)
        {
            if (Audio.AudioManager.Instance != null)
            {
                Audio.AudioManager.Instance.SetMusicVolume(volume);
            }
        }

        private void SetSFXVolume(float volume)
        {
            if (Audio.AudioManager.Instance != null)
            {
                Audio.AudioManager.Instance.SetSFXVolume(volume);
            }
        }

        private void QuitGame()
        {
            PlayButtonSound();
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }

        private void PlayButtonSound()
        {
            if (Audio.AudioManager.Instance != null && buttonClickSound != null)
            {
                Audio.AudioManager.Instance.PlaySFX("ButtonClick");
            }
        }

        private void PlayBackgroundMusic()
        {
            if (Audio.AudioManager.Instance != null && backgroundMusic != null)
            {
                Audio.AudioManager.Instance.PlayMusic("MenuMusic");
            }
        }
    }
}
