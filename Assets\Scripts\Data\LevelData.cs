using UnityEngine;

namespace TowerDefense.Data
{
    [CreateAssetMenu(fileName = "New Level Data", menuName = "Tower Defense/Level Data")]
    public class LevelData : ScriptableObject
    {
        [Header("Level Info")]
        public string levelName;
        public int levelNumber;
        public Sprite levelIcon;

        [Header("Game Settings")]
        public int startingLives;
        public int startingCurrency;
        public WaveData[] waves;

        [Header("Environment")]
        public GameObject levelPrefab;
        public Material skyboxMaterial;
        public Color ambientColor = Color.white;

        [Header("Difficulty Modifiers")]
        [Range(0.5f, 2f)]
        public float enemyHealthMultiplier = 1f;
        [Range(0.5f, 2f)]
        public float enemySpeedMultiplier = 1f;
        [Range(0.5f, 2f)]
        public float enemyRewardMultiplier = 1f;

        [Header("Unlock Requirements")]
        public LevelData previousLevel;
        public bool isUnlocked = false;

        [Header("Audio")]
        public AudioClip backgroundMusic;
        public AudioClip victorySound;
        public AudioClip defeatSound;

        [Header("Description")]
        [TextArea(3, 5)]
        public string description;

        public bool IsUnlocked()
        {
            if (previousLevel == null)
                return true; // First level is always unlocked
            
            return isUnlocked || previousLevel.IsCompleted();
        }

        public bool IsCompleted()
        {
            // Check if level is completed (this would be saved in PlayerPrefs or save system)
            return PlayerPrefs.GetInt($"Level_{levelNumber}_Completed", 0) == 1;
        }

        public void SetCompleted(bool completed)
        {
            PlayerPrefs.SetInt($"Level_{levelNumber}_Completed", completed ? 1 : 0);
            PlayerPrefs.Save();
        }

        public int GetBestScore()
        {
            return PlayerPrefs.GetInt($"Level_{levelNumber}_BestScore", 0);
        }

        public void SetBestScore(int score)
        {
            int currentBest = GetBestScore();
            if (score > currentBest)
            {
                PlayerPrefs.SetInt($"Level_{levelNumber}_BestScore", score);
                PlayerPrefs.Save();
            }
        }
    }
}
