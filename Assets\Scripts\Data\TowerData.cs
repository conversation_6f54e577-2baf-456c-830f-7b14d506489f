using UnityEngine;

namespace TowerDefense.Data
{
    [CreateAssetMenu(fileName = "New Tower Data", menuName = "Tower Defense/Tower Data")]
    public class TowerData : ScriptableObject
    {
        [Header("Basic Stats")]
        public string towerName;
        public Towers.TowerType towerType;
        public int cost;
        public GameObject prefab;
        public Sprite icon;

        [Head<PERSON>("Combat Stats")]
        public float range;
        public float fireRate;
        public int damage;
        public bool useRaycast;

        [Head<PERSON>("Projectile Settings")]
        public GameObject projectilePrefab;
        public float projectileSpeed;
        public float explosionRadius;

        [<PERSON><PERSON>("Upgrade Settings")]
        public TowerUpgrade[] upgrades;

        [Header("Audio")]
        public AudioClip shootSound;
        public AudioClip upgradeSound;

        [Head<PERSON>("Description")]
        [TextArea(3, 5)]
        public string description;
    }

    [System.Serializable]
    public class TowerUpgrade
    {
        public string upgradeName;
        public int cost;
        public float rangeMultiplier = 1f;
        public float fireRateMultiplier = 1f;
        public int damageIncrease = 0;
        public Sprite upgradeIcon;
        
        [TextArea(2, 3)]
        public string description;
    }
}
