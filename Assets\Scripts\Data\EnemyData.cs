using UnityEngine;

namespace TowerDefense.Data
{
    [CreateAssetMenu(fileName = "New Enemy Data", menuName = "Tower Defense/Enemy Data")]
    public class EnemyData : ScriptableObject
    {
        [Header("Basic Info")]
        public string enemyName;
        public Enemies.EnemyType enemyType;
        public GameObject prefab;
        public Sprite icon;

        [Header("Stats")]
        public int health;
        public float moveSpeed;
        public int rewardValue;

        [Head<PERSON>("Special Abilities")]
        public bool canFly;
        public bool isArmored;
        public float armorReduction = 0f; // Percentage damage reduction
        public bool isInvisible;
        public float invisibilityDuration = 0f;

        [Header("Visual Settings")]
        public Material enemyMaterial;
        public GameObject deathEffect;
        public GameObject hitEffect;

        [Header("Audio")]
        public AudioClip deathSound;
        public AudioClip hitSound;
        public AudioClip spawnSound;

        [Header("Description")]
        [TextArea(3, 5)]
        public string description;
    }
}
