using UnityEngine;

namespace TowerDefense.Data
{
    [CreateAssetMenu(fileName = "New Wave Data", menuName = "Tower Defense/Wave Data")]
    public class WaveData : ScriptableObject
    {
        [Header("Wave Info")]
        public string waveName;
        public int waveNumber;

        [Header("Enemy Spawning")]
        public EnemySpawnInfo[] enemySpawns;
        public float timeBetweenSpawns = 1f;
        public float timeBeforeNextWave = 5f;

        [Header("Rewards")]
        public int completionBonus;
        public int perfectBonus; // Bonus for not losing lives

        [Header("Description")]
        [TextArea(2, 4)]
        public string description;
    }

    [System.Serializable]
    public class EnemySpawnInfo
    {
        public EnemyData enemyData;
        public int count;
        public float spawnDelay; // Delay before spawning this enemy type
        public float spawnRate; // Enemies per second
    }
}
