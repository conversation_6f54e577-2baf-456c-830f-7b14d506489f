using UnityEngine;
using UnityEngine.Audio;
using System.Collections.Generic;

namespace TowerDefense.Audio
{
    [System.Serializable]
    public class Sound
    {
        public string name;
        public AudioClip clip;
        [Range(0f, 1f)]
        public float volume = 1f;
        [Range(0.1f, 3f)]
        public float pitch = 1f;
        public bool loop = false;
        public AudioMixerGroup mixerGroup;
        
        [HideInInspector]
        public AudioSource source;
    }

    public class AudioManager : MonoBehaviour
    {
        [Header("Audio Settings")]
        [SerializeField] private AudioMixer audioMixer;
        [SerializeField] private Sound[] sounds;

        [Header("Volume Settings")]
        [Range(0f, 1f)]
        [SerializeField] private float masterVolume = 1f;
        [Range(0f, 1f)]
        [SerializeField] private float musicVolume = 1f;
        [Range(0f, 1f)]
        [SerializeField] private float sfxVolume = 1f;

        public static AudioManager Instance { get; private set; }

        private Dictionary<string, Sound> soundDictionary;

        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudio();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // Play background music
            PlayMusic("BackgroundMusic");
        }

        private void InitializeAudio()
        {
            soundDictionary = new Dictionary<string, Sound>();

            foreach (Sound sound in sounds)
            {
                sound.source = gameObject.AddComponent<AudioSource>();
                sound.source.clip = sound.clip;
                sound.source.volume = sound.volume;
                sound.source.pitch = sound.pitch;
                sound.source.loop = sound.loop;
                sound.source.outputAudioMixerGroup = sound.mixerGroup;

                soundDictionary[sound.name] = sound;
            }

            // Load saved volume settings
            LoadVolumeSettings();
        }

        public void PlaySFX(string soundName)
        {
            if (soundDictionary.TryGetValue(soundName, out Sound sound))
            {
                if (sound.source != null)
                {
                    sound.source.PlayOneShot(sound.clip);
                }
            }
            else
            {
                Debug.LogWarning($"AudioManager: Sound '{soundName}' not found!");
            }
        }

        public void PlayMusic(string soundName)
        {
            if (soundDictionary.TryGetValue(soundName, out Sound sound))
            {
                if (sound.source != null && !sound.source.isPlaying)
                {
                    sound.source.Play();
                }
            }
            else
            {
                Debug.LogWarning($"AudioManager: Music '{soundName}' not found!");
            }
        }

        public void StopMusic(string soundName)
        {
            if (soundDictionary.TryGetValue(soundName, out Sound sound))
            {
                if (sound.source != null && sound.source.isPlaying)
                {
                    sound.source.Stop();
                }
            }
        }

        public void StopAllMusic()
        {
            foreach (Sound sound in sounds)
            {
                if (sound.loop && sound.source != null && sound.source.isPlaying)
                {
                    sound.source.Stop();
                }
            }
        }

        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MasterVolume", VolumeToDecibel(masterVolume));
            }
            SaveVolumeSettings();
        }

        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("MusicVolume", VolumeToDecibel(musicVolume));
            }
            SaveVolumeSettings();
        }

        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            if (audioMixer != null)
            {
                audioMixer.SetFloat("SFXVolume", VolumeToDecibel(sfxVolume));
            }
            SaveVolumeSettings();
        }

        private float VolumeToDecibel(float volume)
        {
            return volume > 0 ? Mathf.Log10(volume) * 20 : -80f;
        }

        private void SaveVolumeSettings()
        {
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", sfxVolume);
            PlayerPrefs.Save();
        }

        private void LoadVolumeSettings()
        {
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 1f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 1f);

            SetMasterVolume(masterVolume);
            SetMusicVolume(musicVolume);
            SetSFXVolume(sfxVolume);
        }

        public float GetMasterVolume() => masterVolume;
        public float GetMusicVolume() => musicVolume;
        public float GetSFXVolume() => sfxVolume;

        public bool IsSoundPlaying(string soundName)
        {
            if (soundDictionary.TryGetValue(soundName, out Sound sound))
            {
                return sound.source != null && sound.source.isPlaying;
            }
            return false;
        }

        public void PauseAllAudio()
        {
            foreach (Sound sound in sounds)
            {
                if (sound.source != null && sound.source.isPlaying)
                {
                    sound.source.Pause();
                }
            }
        }

        public void ResumeAllAudio()
        {
            foreach (Sound sound in sounds)
            {
                if (sound.source != null)
                {
                    sound.source.UnPause();
                }
            }
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}
