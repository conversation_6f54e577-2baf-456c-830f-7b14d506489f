using UnityEngine;
using UnityEngine.UI;

namespace TowerDefense.UI
{
    public class HealthBar : MonoBehaviour
    {
        [Header("Health Bar Settings")]
        [SerializeField] private Slider healthSlider;
        [SerializeField] private Image fillImage;
        [SerializeField] private Gradient healthGradient;
        [SerializeField] private bool followTarget = true;
        [SerializeField] private Vector3 offset = Vector3.up * 2f;

        [Header("Animation")]
        [SerializeField] private bool animateChanges = true;
        [SerializeField] private float animationSpeed = 5f;

        private Enemies.Enemy targetEnemy;
        private Camera playerCamera;
        private Canvas canvas;
        private float targetHealthPercentage;
        private float currentDisplayedHealth;

        private void Start()
        {
            playerCamera = Camera.main;
            canvas = GetComponentInParent<Canvas>();
            
            if (healthSlider == null)
                healthSlider = GetComponent<Slider>();
            
            if (fillImage == null && healthSlider != null)
                fillImage = healthSlider.fillRect.GetComponent<Image>();
        }

        private void Update()
        {
            if (targetEnemy == null)
            {
                Destroy(gameObject);
                return;
            }

            if (followTarget)
            {
                UpdatePosition();
            }

            if (animateChanges)
            {
                AnimateHealthChange();
            }

            UpdateHealthDisplay();
        }

        public void Initialize(Enemies.Enemy enemy)
        {
            targetEnemy = enemy;
            targetHealthPercentage = enemy.HealthPercentage;
            currentDisplayedHealth = targetHealthPercentage;
            
            UpdateHealthDisplay();
        }

        public void UpdateHealth()
        {
            if (targetEnemy == null)
                return;

            targetHealthPercentage = targetEnemy.HealthPercentage;
            
            if (!animateChanges)
            {
                currentDisplayedHealth = targetHealthPercentage;
                UpdateHealthDisplay();
            }
        }

        private void UpdatePosition()
        {
            if (targetEnemy == null || playerCamera == null)
                return;

            Vector3 worldPosition = targetEnemy.transform.position + offset;
            
            if (canvas.renderMode == RenderMode.ScreenSpaceOverlay)
            {
                Vector3 screenPosition = playerCamera.WorldToScreenPoint(worldPosition);
                transform.position = screenPosition;
            }
            else if (canvas.renderMode == RenderMode.WorldSpace)
            {
                transform.position = worldPosition;
                transform.LookAt(transform.position + playerCamera.transform.rotation * Vector3.forward,
                                playerCamera.transform.rotation * Vector3.up);
            }
        }

        private void AnimateHealthChange()
        {
            if (Mathf.Abs(currentDisplayedHealth - targetHealthPercentage) > 0.01f)
            {
                currentDisplayedHealth = Mathf.Lerp(currentDisplayedHealth, targetHealthPercentage, 
                                                   animationSpeed * Time.deltaTime);
            }
            else
            {
                currentDisplayedHealth = targetHealthPercentage;
            }
        }

        private void UpdateHealthDisplay()
        {
            if (healthSlider != null)
            {
                healthSlider.value = currentDisplayedHealth;
            }

            if (fillImage != null && healthGradient != null)
            {
                fillImage.color = healthGradient.Evaluate(currentDisplayedHealth);
            }

            // Hide health bar when at full health (optional)
            bool shouldShow = currentDisplayedHealth < 1f;
            if (gameObject.activeSelf != shouldShow)
            {
                gameObject.SetActive(shouldShow);
            }
        }

        public void SetHealthGradient(Gradient gradient)
        {
            healthGradient = gradient;
        }

        public void SetOffset(Vector3 newOffset)
        {
            offset = newOffset;
        }

        public void SetFollowTarget(bool follow)
        {
            followTarget = follow;
        }
    }
}
