using UnityEngine;

namespace TowerDefense.Enemies
{
    public class Waypoint : MonoBeh<PERSON><PERSON>
    {
        [Head<PERSON>("Waypoint Settings")]
        [SerializeField] private bool showGizmo = true;
        [SerializeField] private Color gizmoColor = Color.blue;
        [SerializeField] private float gizmoSize = 0.5f;

        [Header("Path Settings")]
        [SerializeField] private Waypoint nextWaypoint;
        [SerializeField] private float waitTime = 0f; // Time to wait at this waypoint

        public Waypoint NextWaypoint => nextWaypoint;
        public float WaitTime => waitTime;

        private void Start()
        {
            // Hide the waypoint renderer in play mode
            Renderer renderer = GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.enabled = false;
            }
        }

        public void SetNextWaypoint(Waypoint next)
        {
            nextWaypoint = next;
        }

        private void OnDrawGizmos()
        {
            if (!showGizmo)
                return;

            // Draw waypoint
            Gizmos.color = gizmoColor;
            Gizmos.DrawWireSphere(transform.position, gizmoSize);

            // Draw connection to next waypoint
            if (nextWaypoint != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(transform.position, nextWaypoint.transform.position);
                
                // Draw arrow direction
                Vector3 direction = (nextWaypoint.transform.position - transform.position).normalized;
                Vector3 arrowHead = nextWaypoint.transform.position - direction * 0.5f;
                Vector3 right = Vector3.Cross(direction, Vector3.up) * 0.2f;
                Vector3 left = -right;
                
                Gizmos.DrawLine(arrowHead + right, nextWaypoint.transform.position);
                Gizmos.DrawLine(arrowHead + left, nextWaypoint.transform.position);
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Highlight selected waypoint
            Gizmos.color = Color.red;
            Gizmos.DrawSphere(transform.position, gizmoSize);
        }
    }
}
