using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace TowerDefense.UI
{
    public class LevelButton : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Button button;
        [SerializeField] private TextMeshProUGUI levelNameText;
        [SerializeField] private TextMeshP<PERSON>UGUI levelNumberText;
        [SerializeField] private Image levelIcon;
        [SerializeField] private Image lockIcon;
        [SerializeField] private GameObject completedIndicator;
        [SerializeField] private TextMeshProUGUI bestScoreText;

        [Header("Visual States")]
        [SerializeField] private Color unlockedColor = Color.white;
        [SerializeField] private Color lockedColor = Color.gray;
        [SerializeField] private Color completedColor = Color.green;

        private Data.LevelData levelData;
        private System.Action<Data.LevelData> onLevelSelected;

        private void Start()
        {
            if (button == null)
                button = GetComponent<Button>();
        }

        public void Initialize(Data.LevelData data, System.Action<Data.LevelData> onSelected)
        {
            levelData = data;
            onLevelSelected = onSelected;

            UpdateDisplay();
            SetupButton();
        }

        private void UpdateDisplay()
        {
            if (levelData == null)
                return;

            // Set level name
            if (levelNameText != null)
                levelNameText.text = levelData.levelName;

            // Set level number
            if (levelNumberText != null)
                levelNumberText.text = levelData.levelNumber.ToString();

            // Set level icon
            if (levelIcon != null && levelData.levelIcon != null)
                levelIcon.sprite = levelData.levelIcon;

            // Update visual state based on unlock status
            bool isUnlocked = levelData.IsUnlocked();
            bool isCompleted = levelData.IsCompleted();

            UpdateVisualState(isUnlocked, isCompleted);
            UpdateBestScore();
        }

        private void UpdateVisualState(bool isUnlocked, bool isCompleted)
        {
            // Update button interactability
            if (button != null)
                button.interactable = isUnlocked;

            // Show/hide lock icon
            if (lockIcon != null)
                lockIcon.gameObject.SetActive(!isUnlocked);

            // Show/hide completed indicator
            if (completedIndicator != null)
                completedIndicator.SetActive(isCompleted);

            // Update colors
            Color targetColor = lockedColor;
            if (isCompleted)
                targetColor = completedColor;
            else if (isUnlocked)
                targetColor = unlockedColor;

            // Apply color to various UI elements
            if (levelIcon != null)
                levelIcon.color = targetColor;

            if (levelNameText != null)
                levelNameText.color = targetColor;

            if (levelNumberText != null)
                levelNumberText.color = targetColor;
        }

        private void UpdateBestScore()
        {
            if (bestScoreText != null && levelData != null)
            {
                int bestScore = levelData.GetBestScore();
                if (bestScore > 0)
                {
                    bestScoreText.text = $"Best: {bestScore:N0}";
                    bestScoreText.gameObject.SetActive(true);
                }
                else
                {
                    bestScoreText.gameObject.SetActive(false);
                }
            }
        }

        private void SetupButton()
        {
            if (button != null)
            {
                button.onClick.RemoveAllListeners();
                button.onClick.AddListener(OnButtonClicked);
            }
        }

        private void OnButtonClicked()
        {
            if (levelData != null && levelData.IsUnlocked())
            {
                onLevelSelected?.Invoke(levelData);
            }
        }

        public void RefreshDisplay()
        {
            UpdateDisplay();
        }

        public Data.LevelData GetLevelData()
        {
            return levelData;
        }
    }
}
