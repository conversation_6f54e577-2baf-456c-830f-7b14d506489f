using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace TowerDefense.Core
{
    [System.Serializable]
    public class Wave
    {
        public string waveName;
        public GameObject enemyPrefab;
        public int enemyCount;
        public float spawnRate;
        public float timeBetweenWaves;
    }

    public class WaveSpawner : MonoBehaviour
    {
        [Header("Wave Configuration")]
        [SerializeField] private Wave[] waves;
        [SerializeField] private Transform spawnPoint;
        [SerializeField] private bool autoStartWaves = false;
        [SerializeField] private float countdownTime = 5f;

        [Header("Debug")]
        [SerializeField] private bool showDebugInfo = true;

        private int currentWaveIndex = 0;
        private float countdown;
        private bool waveInProgress = false;
        private int enemiesAlive = 0;
        private Coroutine spawnCoroutine;

        // Events
        public System.Action<Wave> OnWaveStart;
        public System.Action<Wave> OnWaveComplete;
        public System.Action<float> OnCountdownUpdate;

        private void Start()
        {
            countdown = countdownTime;
            
            if (spawnPoint == null)
            {
                Debug.LogError("WaveSpawner: No spawn point assigned!");
            }

            if (waves.Length == 0)
            {
                Debug.LogError("WaveSpawner: No waves configured!");
            }
        }

        private void Update()
        {
            if (GameManager.Instance == null || GameManager.Instance.CurrentState != GameState.Playing)
                return;

            if (autoStartWaves && !waveInProgress && currentWaveIndex < waves.Length)
            {
                if (countdown <= 0f)
                {
                    StartWave();
                }
                else
                {
                    countdown -= Time.deltaTime;
                    OnCountdownUpdate?.Invoke(countdown);
                }
            }

            if (showDebugInfo)
            {
                DebugDisplay();
            }
        }

        public void StartWave()
        {
            if (waveInProgress || currentWaveIndex >= waves.Length)
                return;

            Wave currentWave = waves[currentWaveIndex];
            waveInProgress = true;
            enemiesAlive = currentWave.enemyCount;

            // Update GameManager
            GameManager.Instance.SetEnemiesRemaining(enemiesAlive);

            // Start spawning
            spawnCoroutine = StartCoroutine(SpawnWave(currentWave));

            OnWaveStart?.Invoke(currentWave);

            if (showDebugInfo)
            {
                Debug.Log($"Starting Wave {currentWaveIndex + 1}: {currentWave.waveName}");
            }
        }

        private IEnumerator SpawnWave(Wave wave)
        {
            for (int i = 0; i < wave.enemyCount; i++)
            {
                SpawnEnemy(wave.enemyPrefab);
                yield return new WaitForSeconds(1f / wave.spawnRate);
            }
        }

        private void SpawnEnemy(GameObject enemyPrefab)
        {
            if (spawnPoint == null || enemyPrefab == null)
                return;

            GameObject enemyObject = Instantiate(enemyPrefab, spawnPoint.position, spawnPoint.rotation);
            
            // Get enemy component and subscribe to death event
            Enemy enemy = enemyObject.GetComponent<Enemy>();
            if (enemy != null)
            {
                enemy.OnEnemyDeath += OnEnemyDeath;
                enemy.OnEnemyReachedEnd += OnEnemyReachedEnd;
            }
        }

        private void OnEnemyDeath(Enemy enemy, int reward)
        {
            enemiesAlive--;
            GameManager.Instance.SetEnemiesRemaining(enemiesAlive);
            GameManager.Instance.AddCurrency(reward);

            // Unsubscribe from events
            enemy.OnEnemyDeath -= OnEnemyDeath;
            enemy.OnEnemyReachedEnd -= OnEnemyReachedEnd;

            CheckWaveComplete();
        }

        private void OnEnemyReachedEnd(Enemy enemy)
        {
            enemiesAlive--;
            GameManager.Instance.SetEnemiesRemaining(enemiesAlive);
            GameManager.Instance.LoseLife();

            // Unsubscribe from events
            enemy.OnEnemyDeath -= OnEnemyDeath;
            enemy.OnEnemyReachedEnd -= OnEnemyReachedEnd;

            CheckWaveComplete();
        }

        private void CheckWaveComplete()
        {
            if (enemiesAlive <= 0 && waveInProgress)
            {
                WaveComplete();
            }
        }

        private void WaveComplete()
        {
            waveInProgress = false;
            Wave completedWave = waves[currentWaveIndex];
            
            OnWaveComplete?.Invoke(completedWave);

            if (showDebugInfo)
            {
                Debug.Log($"Wave {currentWaveIndex + 1} Complete: {completedWave.waveName}");
            }

            currentWaveIndex++;

            if (currentWaveIndex < waves.Length)
            {
                // Prepare next wave
                countdown = waves[currentWaveIndex].timeBetweenWaves;
            }
            else
            {
                // All waves complete
                if (showDebugInfo)
                {
                    Debug.Log("All waves complete!");
                }
                GameManager.Instance.Victory();
            }
        }

        public bool IsLastWave()
        {
            return currentWaveIndex >= waves.Length;
        }

        public Wave GetCurrentWave()
        {
            if (currentWaveIndex < waves.Length)
                return waves[currentWaveIndex];
            return null;
        }

        public int GetCurrentWaveIndex()
        {
            return currentWaveIndex;
        }

        public int GetTotalWaves()
        {
            return waves.Length;
        }

        public float GetCountdown()
        {
            return countdown;
        }

        public bool IsWaveInProgress()
        {
            return waveInProgress;
        }

        // Manual wave start (for UI button)
        public void StartWaveManually()
        {
            if (!autoStartWaves && !waveInProgress && currentWaveIndex < waves.Length)
            {
                countdown = 0f;
                StartWave();
            }
        }

        private void DebugDisplay()
        {
            if (Application.isPlaying)
            {
                string debugText = $"Wave: {currentWaveIndex + 1}/{waves.Length}\n";
                debugText += $"Enemies Alive: {enemiesAlive}\n";
                debugText += $"Wave In Progress: {waveInProgress}\n";
                debugText += $"Countdown: {countdown:F1}";
                
                // This would be displayed in UI in a real implementation
                // For now, it's just for debugging purposes
            }
        }

        private void OnDestroy()
        {
            if (spawnCoroutine != null)
            {
                StopCoroutine(spawnCoroutine);
            }
        }
    }
}
