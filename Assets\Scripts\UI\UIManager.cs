using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace TowerDefense.UI
{
    public class UIManager : MonoBehavi<PERSON>
    {
        [Header("HUD Elements")]
        [SerializeField] private TextMeshProU<PERSON><PERSON> livesText;
        [SerializeField] private TextMeshP<PERSON><PERSON><PERSON><PERSON> currencyText;
        [SerializeField] private TextMesh<PERSON><PERSON>UG<PERSON> waveText;
        [SerializeField] private But<PERSON> pauseButton;
        [SerializeField] private Button startWaveButton;

        [Header("Tower Selection")]
        [SerializeField] private GameObject towerSelectionPanel;
        [SerializeField] private Button[] towerButtons;

        [Header("Tower Upgrade")]
        [SerializeField] private GameObject towerUpgradePanel;
        [SerializeField] private Button upgradeButton;
        [SerializeField] private Button sellButton;
        [SerializeField] private TextMesh<PERSON>roUGUI towerInfoText;

        [Header("Game State Panels")]
        [SerializeField] private GameObject pausePanel;
        [SerializeField] private GameObject gameOverPanel;
        [SerializeField] private GameObject victoryPanel;

        [Header("Pause Panel Buttons")]
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button restartButton;
        [SerializeField] private Button mainMenuButton;

        [Header("Game Over Panel Buttons")]
        [SerializeField] private Button retryButton;
        [SerializeField] private Button gameOverMainMenuButton;

        [Header("Victory Panel Buttons")]
        [SerializeField] private Button nextLevelButton;
        [SerializeField] private Button victoryMainMenuButton;

        private TouchInput touchInput;
        private Core.WaveSpawner waveSpawner;
        private Towers.Tower selectedTower;

        private void Start()
        {
            InitializeUI();
            SetupEventListeners();
            FindComponents();
        }

        private void InitializeUI()
        {
            // Hide all panels initially
            if (pausePanel != null) pausePanel.SetActive(false);
            if (gameOverPanel != null) gameOverPanel.SetActive(false);
            if (victoryPanel != null) victoryPanel.SetActive(false);
            if (towerUpgradePanel != null) towerUpgradePanel.SetActive(false);
            
            // Show tower selection panel
            if (towerSelectionPanel != null) towerSelectionPanel.SetActive(true);
        }

        private void SetupEventListeners()
        {
            // HUD Buttons
            if (pauseButton != null)
                pauseButton.onClick.AddListener(PauseGame);
            
            if (startWaveButton != null)
                startWaveButton.onClick.AddListener(StartWave);

            // Tower Selection Buttons
            for (int i = 0; i < towerButtons.Length; i++)
            {
                int towerIndex = i; // Capture for closure
                if (towerButtons[i] != null)
                {
                    towerButtons[i].onClick.AddListener(() => SelectTowerForPlacement(towerIndex));
                }
            }

            // Tower Upgrade Buttons
            if (upgradeButton != null)
                upgradeButton.onClick.AddListener(UpgradeTower);
            
            if (sellButton != null)
                sellButton.onClick.AddListener(SellTower);

            // Pause Panel Buttons
            if (resumeButton != null)
                resumeButton.onClick.AddListener(ResumeGame);
            
            if (restartButton != null)
                restartButton.onClick.AddListener(RestartLevel);
            
            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(GoToMainMenu);

            // Game Over Panel Buttons
            if (retryButton != null)
                retryButton.onClick.AddListener(RestartLevel);
            
            if (gameOverMainMenuButton != null)
                gameOverMainMenuButton.onClick.AddListener(GoToMainMenu);

            // Victory Panel Buttons
            if (nextLevelButton != null)
                nextLevelButton.onClick.AddListener(LoadNextLevel);
            
            if (victoryMainMenuButton != null)
                victoryMainMenuButton.onClick.AddListener(GoToMainMenu);

            // Subscribe to GameManager events
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.OnLivesChanged += UpdateLivesDisplay;
                Core.GameManager.Instance.OnCurrencyChanged += UpdateCurrencyDisplay;
                Core.GameManager.Instance.OnWaveChanged += UpdateWaveDisplay;
                Core.GameManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
        }

        private void FindComponents()
        {
            touchInput = FindObjectOfType<TouchInput>();
            waveSpawner = FindObjectOfType<Core.WaveSpawner>();
        }

        private void UpdateLivesDisplay(int lives)
        {
            if (livesText != null)
                livesText.text = $"Lives: {lives}";
        }

        private void UpdateCurrencyDisplay(int currency)
        {
            if (currencyText != null)
                currencyText.text = $"${currency}";
        }

        private void UpdateWaveDisplay(int wave)
        {
            if (waveText != null)
            {
                if (waveSpawner != null)
                {
                    waveText.text = $"Wave: {wave}/{waveSpawner.GetTotalWaves()}";
                }
                else
                {
                    waveText.text = $"Wave: {wave}";
                }
            }
        }

        private void OnGameStateChanged(Core.GameState newState)
        {
            switch (newState)
            {
                case Core.GameState.Paused:
                    ShowPausePanel();
                    break;
                case Core.GameState.GameOver:
                    ShowGameOverPanel();
                    break;
                case Core.GameState.Victory:
                    ShowVictoryPanel();
                    break;
                case Core.GameState.Playing:
                    HideAllPanels();
                    break;
            }
        }

        private void SelectTowerForPlacement(int towerIndex)
        {
            if (touchInput != null)
            {
                touchInput.StartTowerPlacement(towerIndex);
            }
        }

        private void PauseGame()
        {
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.PauseGame();
            }
        }

        private void ResumeGame()
        {
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.ResumeGame();
            }
        }

        private void StartWave()
        {
            if (waveSpawner != null)
            {
                waveSpawner.StartWaveManually();
            }
        }

        public void ShowTowerUpgradeUI(Towers.Tower tower)
        {
            selectedTower = tower;
            
            if (towerUpgradePanel != null)
            {
                towerUpgradePanel.SetActive(true);
                UpdateTowerInfo();
            }
        }

        public void HideTowerUpgradeUI()
        {
            selectedTower = null;
            
            if (towerUpgradePanel != null)
            {
                towerUpgradePanel.SetActive(false);
            }
        }

        private void UpdateTowerInfo()
        {
            if (selectedTower == null || towerInfoText == null)
                return;

            string info = $"Tower Type: {selectedTower.Type}\n";
            info += $"Damage: {selectedTower.Damage}\n";
            info += $"Range: {selectedTower.Range:F1}\n";
            info += $"Fire Rate: {selectedTower.FireRate:F1}";
            
            towerInfoText.text = info;
        }

        private void UpgradeTower()
        {
            if (selectedTower == null)
                return;

            // Implement upgrade logic
            int upgradeCost = 50; // This should be configurable
            
            if (Core.GameManager.Instance != null && Core.GameManager.Instance.SpendCurrency(upgradeCost))
            {
                selectedTower.UpgradeTower(1.2f, 1.1f, 10); // Example upgrade values
                UpdateTowerInfo();
                
                // Play upgrade sound
                AudioManager.Instance?.PlaySFX("TowerUpgrade");
            }
        }

        private void SellTower()
        {
            if (selectedTower == null)
                return;

            // Give back some currency
            int sellValue = selectedTower.Cost / 2;
            Core.GameManager.Instance?.AddCurrency(sellValue);
            
            // Destroy tower
            Destroy(selectedTower.gameObject);
            
            // Hide upgrade UI
            HideTowerUpgradeUI();
            
            // Play sell sound
            AudioManager.Instance?.PlaySFX("TowerSell");
        }

        private void ShowPausePanel()
        {
            if (pausePanel != null)
                pausePanel.SetActive(true);
        }

        public void ShowGameOverPanel()
        {
            if (gameOverPanel != null)
                gameOverPanel.SetActive(true);
        }

        private void ShowVictoryPanel()
        {
            if (victoryPanel != null)
                victoryPanel.SetActive(true);
        }

        private void HideAllPanels()
        {
            if (pausePanel != null) pausePanel.SetActive(false);
            if (gameOverPanel != null) gameOverPanel.SetActive(false);
            if (victoryPanel != null) victoryPanel.SetActive(false);
        }

        private void RestartLevel()
        {
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.RestartLevel();
            }
        }

        private void GoToMainMenu()
        {
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.LoadMainMenu();
            }
        }

        private void LoadNextLevel()
        {
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.LoadNextLevel();
            }
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            if (Core.GameManager.Instance != null)
            {
                Core.GameManager.Instance.OnLivesChanged -= UpdateLivesDisplay;
                Core.GameManager.Instance.OnCurrencyChanged -= UpdateCurrencyDisplay;
                Core.GameManager.Instance.OnWaveChanged -= UpdateWaveDisplay;
                Core.GameManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }
    }
}
