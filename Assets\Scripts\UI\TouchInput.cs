using UnityEngine;
using UnityEngine.EventSystems;

namespace TowerDefense.UI
{
    public class TouchInput : MonoBehaviour
    {
        [Header("Touch Settings")]
        [SerializeField] private LayerMask buildableLayer = 1;
        [SerializeField] private LayerMask towerLayer = 1 << 8;
        [SerializeField] private Camera playerCamera;

        [Header("Tower Placement")]
        [SerializeField] private GameObject[] towerPrefabs;
        [SerializeField] private Material previewMaterial;
        [SerializeField] private Material validPlacementMaterial;
        [SerializeField] private Material invalidPlacementMaterial;

        private GameObject selectedTowerPrefab;
        private GameObject previewTower;
        private bool isPlacingTower = false;
        private Towers.Tower selectedTower;
        private UIManager uiManager;

        // Events
        public System.Action<Towers.Tower> OnTowerSelected;
        public System.Action<Towers.Tower> OnTowerPlaced;
        public System.Action OnTowerDeselected;

        private void Start()
        {
            if (playerCamera == null)
                playerCamera = Camera.main;

            uiManager = FindObjectOfType<UIManager>();
        }

        private void Update()
        {
            HandleInput();
            UpdatePreviewTower();
        }

        private void HandleInput()
        {
            // Handle mouse input for testing in editor
            if (Application.isEditor)
            {
                HandleMouseInput();
            }
            
            // Handle touch input for mobile
            HandleTouchInput();
        }

        private void HandleMouseInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                Vector3 mousePosition = Input.mousePosition;
                HandleTouch(mousePosition);
            }
        }

        private void HandleTouchInput()
        {
            if (Input.touchCount > 0)
            {
                Touch touch = Input.GetTouch(0);
                
                if (touch.phase == TouchPhase.Began)
                {
                    HandleTouch(touch.position);
                }
            }
        }

        private void HandleTouch(Vector3 screenPosition)
        {
            // Check if touching UI
            if (EventSystem.current.IsPointerOverGameObject())
                return;

            Ray ray = playerCamera.ScreenPointToRay(screenPosition);
            RaycastHit hit;

            if (isPlacingTower)
            {
                HandleTowerPlacement(ray);
            }
            else
            {
                HandleTowerSelection(ray);
            }
        }

        private void HandleTowerPlacement(Ray ray)
        {
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, buildableLayer))
            {
                if (CanPlaceTowerAt(hit.point))
                {
                    PlaceTower(hit.point);
                }
            }
            else
            {
                // Cancel placement if touching outside buildable area
                CancelTowerPlacement();
            }
        }

        private void HandleTowerSelection(Ray ray)
        {
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, towerLayer))
            {
                Towers.Tower tower = hit.collider.GetComponent<Towers.Tower>();
                if (tower != null)
                {
                    SelectTower(tower);
                }
            }
            else
            {
                // Deselect tower if touching empty space
                DeselectTower();
            }
        }

        private void UpdatePreviewTower()
        {
            if (!isPlacingTower || previewTower == null)
                return;

            Vector3 mousePosition = Input.mousePosition;
            if (Input.touchCount > 0)
            {
                mousePosition = Input.GetTouch(0).position;
            }

            Ray ray = playerCamera.ScreenPointToRay(mousePosition);
            
            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, buildableLayer))
            {
                previewTower.transform.position = hit.point;
                
                // Update preview material based on placement validity
                bool canPlace = CanPlaceTowerAt(hit.point);
                UpdatePreviewMaterial(canPlace);
            }
        }

        public void StartTowerPlacement(int towerIndex)
        {
            if (towerIndex < 0 || towerIndex >= towerPrefabs.Length)
                return;

            selectedTowerPrefab = towerPrefabs[towerIndex];
            isPlacingTower = true;

            CreatePreviewTower();
            DeselectTower(); // Deselect any currently selected tower
        }

        private void CreatePreviewTower()
        {
            if (selectedTowerPrefab == null)
                return;

            previewTower = Instantiate(selectedTowerPrefab);
            
            // Disable tower functionality for preview
            Towers.Tower towerComponent = previewTower.GetComponent<Towers.Tower>();
            if (towerComponent != null)
            {
                towerComponent.enabled = false;
            }

            // Disable colliders
            Collider[] colliders = previewTower.GetComponentsInChildren<Collider>();
            foreach (Collider col in colliders)
            {
                col.enabled = false;
            }

            // Set preview material
            UpdatePreviewMaterial(false);
        }

        private void UpdatePreviewMaterial(bool isValid)
        {
            if (previewTower == null)
                return;

            Material materialToUse = isValid ? validPlacementMaterial : invalidPlacementMaterial;
            
            Renderer[] renderers = previewTower.GetComponentsInChildren<Renderer>();
            foreach (Renderer renderer in renderers)
            {
                renderer.material = materialToUse;
            }
        }

        private bool CanPlaceTowerAt(Vector3 position)
        {
            // Check if there's already a tower at this position
            Collider[] overlapping = Physics.OverlapSphere(position, 1f, towerLayer);
            if (overlapping.Length > 0)
                return false;

            // Check if we have enough currency
            if (selectedTowerPrefab != null)
            {
                Towers.Tower towerComponent = selectedTowerPrefab.GetComponent<Towers.Tower>();
                if (towerComponent != null && Core.GameManager.Instance != null)
                {
                    return Core.GameManager.Instance.Currency >= towerComponent.Cost;
                }
            }

            return true;
        }

        private void PlaceTower(Vector3 position)
        {
            if (selectedTowerPrefab == null)
                return;

            // Check cost
            Towers.Tower towerComponent = selectedTowerPrefab.GetComponent<Towers.Tower>();
            if (towerComponent != null && Core.GameManager.Instance != null)
            {
                if (Core.GameManager.Instance.SpendCurrency(towerComponent.Cost))
                {
                    // Place the tower
                    GameObject newTower = Instantiate(selectedTowerPrefab, position, Quaternion.identity);
                    Towers.Tower newTowerComponent = newTower.GetComponent<Towers.Tower>();
                    
                    OnTowerPlaced?.Invoke(newTowerComponent);
                    
                    // Play placement sound
                    AudioManager.Instance?.PlaySFX("TowerPlace");
                }
            }

            CancelTowerPlacement();
        }

        public void CancelTowerPlacement()
        {
            isPlacingTower = false;
            selectedTowerPrefab = null;

            if (previewTower != null)
            {
                Destroy(previewTower);
                previewTower = null;
            }
        }

        private void SelectTower(Towers.Tower tower)
        {
            // Deselect previous tower
            if (selectedTower != null)
            {
                selectedTower.DeselectTower();
            }

            selectedTower = tower;
            selectedTower.SelectTower();
            
            OnTowerSelected?.Invoke(selectedTower);
            
            // Show upgrade UI
            if (uiManager != null)
            {
                uiManager.ShowTowerUpgradeUI(selectedTower);
            }
        }

        private void DeselectTower()
        {
            if (selectedTower != null)
            {
                selectedTower.DeselectTower();
                selectedTower = null;
                
                OnTowerDeselected?.Invoke();
                
                // Hide upgrade UI
                if (uiManager != null)
                {
                    uiManager.HideTowerUpgradeUI();
                }
            }
        }

        public Towers.Tower GetSelectedTower()
        {
            return selectedTower;
        }

        public bool IsPlacingTower()
        {
            return isPlacingTower;
        }
    }
}
