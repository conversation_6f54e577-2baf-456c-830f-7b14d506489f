using UnityEngine;
using UnityEngine.SceneManagement;

namespace TowerDefense.Core
{
    public class LevelManager : MonoBehaviour
    {
        [Header("Level Settings")]
        [SerializeField] private Data.LevelData currentLevelData;
        [SerializeField] private Transform spawnPoint;
        [SerializeField] private Transform[] waypointPath;

        [Header("Build Zones")]
        [SerializeField] private Transform[] buildZones;
        [SerializeField] private LayerMask buildableLayer = 1;

        public static LevelManager Instance { get; private set; }
        public Data.LevelData CurrentLevelData => currentLevelData;

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            InitializeLevel();
        }

        private void Start()
        {
            SetupLevel();
        }

        private void InitializeLevel()
        {
            if (currentLevelData != null)
            {
                // Apply level settings to GameManager
                if (GameManager.Instance != null)
                {
                    // This would require modifying GameManager to accept these values
                    // GameManager.Instance.SetStartingValues(currentLevelData.startingLives, currentLevelData.startingCurrency);
                }

                // Set environment settings
                SetupEnvironment();
            }
        }

        private void SetupLevel()
        {
            // Setup waypoints
            SetupWaypoints();

            // Setup build zones
            SetupBuildZones();

            // Setup wave spawner with level data
            SetupWaveSpawner();

            // Play background music
            if (currentLevelData != null && currentLevelData.backgroundMusic != null)
            {
                Audio.AudioManager.Instance?.PlayMusic("BackgroundMusic");
            }
        }

        private void SetupEnvironment()
        {
            if (currentLevelData == null)
                return;

            // Set skybox
            if (currentLevelData.skyboxMaterial != null)
            {
                RenderSettings.skybox = currentLevelData.skyboxMaterial;
            }

            // Set ambient lighting
            RenderSettings.ambientLight = currentLevelData.ambientColor;
        }

        private void SetupWaypoints()
        {
            if (waypointPath == null || waypointPath.Length == 0)
            {
                Debug.LogWarning("LevelManager: No waypoint path defined!");
                return;
            }

            // Create waypoint objects if they don't exist
            GameObject waypointContainer = GameObject.FindGameObjectWithTag("Waypoints");
            if (waypointContainer == null)
            {
                waypointContainer = new GameObject("Waypoints");
                waypointContainer.tag = "Waypoints";
            }

            // Add waypoint components to path transforms
            for (int i = 0; i < waypointPath.Length; i++)
            {
                if (waypointPath[i] != null)
                {
                    Enemies.Waypoint waypoint = waypointPath[i].GetComponent<Enemies.Waypoint>();
                    if (waypoint == null)
                    {
                        waypoint = waypointPath[i].gameObject.AddComponent<Enemies.Waypoint>();
                    }

                    // Set next waypoint
                    if (i < waypointPath.Length - 1)
                    {
                        Enemies.Waypoint nextWaypoint = waypointPath[i + 1].GetComponent<Enemies.Waypoint>();
                        if (nextWaypoint == null)
                        {
                            nextWaypoint = waypointPath[i + 1].gameObject.AddComponent<Enemies.Waypoint>();
                        }
                        waypoint.SetNextWaypoint(nextWaypoint);
                    }

                    // Parent to waypoint container
                    waypointPath[i].SetParent(waypointContainer.transform);
                }
            }
        }

        private void SetupBuildZones()
        {
            if (buildZones == null)
                return;

            foreach (Transform buildZone in buildZones)
            {
                if (buildZone != null)
                {
                    // Ensure build zones have the correct layer
                    buildZone.gameObject.layer = LayerMaskToLayer(buildableLayer);

                    // Add collider if missing
                    Collider collider = buildZone.GetComponent<Collider>();
                    if (collider == null)
                    {
                        BoxCollider boxCollider = buildZone.gameObject.AddComponent<BoxCollider>();
                        boxCollider.isTrigger = true;
                    }
                }
            }
        }

        private void SetupWaveSpawner()
        {
            WaveSpawner waveSpawner = FindObjectOfType<WaveSpawner>();
            if (waveSpawner != null && currentLevelData != null && currentLevelData.waves != null)
            {
                // This would require modifying WaveSpawner to accept WaveData
                // waveSpawner.SetWaveData(currentLevelData.waves);
            }

            // Set spawn point
            if (spawnPoint != null && waveSpawner != null)
            {
                // waveSpawner.SetSpawnPoint(spawnPoint);
            }
        }

        public Vector3 GetSpawnPoint()
        {
            return spawnPoint != null ? spawnPoint.position : Vector3.zero;
        }

        public Transform[] GetWaypointPath()
        {
            return waypointPath;
        }

        public bool IsPositionBuildable(Vector3 position)
        {
            if (buildZones == null)
                return true; // If no build zones defined, allow building anywhere

            foreach (Transform buildZone in buildZones)
            {
                if (buildZone != null)
                {
                    Collider collider = buildZone.GetComponent<Collider>();
                    if (collider != null && collider.bounds.Contains(position))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        public void CompleteLevel()
        {
            if (currentLevelData != null)
            {
                currentLevelData.SetCompleted(true);
                
                // Calculate score (this could be more complex)
                int score = CalculateLevelScore();
                currentLevelData.SetBestScore(score);

                // Play victory sound
                if (currentLevelData.victorySound != null)
                {
                    Audio.AudioManager.Instance?.PlaySFX("Victory");
                }
            }
        }

        public void FailLevel()
        {
            if (currentLevelData != null && currentLevelData.defeatSound != null)
            {
                Audio.AudioManager.Instance?.PlaySFX("Defeat");
            }
        }

        private int CalculateLevelScore()
        {
            int score = 0;
            
            if (GameManager.Instance != null)
            {
                // Base score from remaining lives and currency
                score += GameManager.Instance.Lives * 100;
                score += GameManager.Instance.Currency * 10;
                
                // Bonus for completing waves quickly
                // This would require tracking completion time
            }

            return score;
        }

        private int LayerMaskToLayer(LayerMask layerMask)
        {
            int layer = 0;
            int mask = layerMask.value;
            while (mask > 1)
            {
                mask >>= 1;
                layer++;
            }
            return layer;
        }

        public void SetLevelData(Data.LevelData levelData)
        {
            currentLevelData = levelData;
            InitializeLevel();
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}
