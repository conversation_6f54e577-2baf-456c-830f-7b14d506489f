using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

namespace TowerDefense.Core
{
    public enum GameState
    {
        Start,
        Playing,
        Paused,
        GameOver,
        Victory
    }

    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private int startingLives = 20;
        [SerializeField] private int startingCurrency = 100;
        [SerializeField] private float gameOverDelay = 2f;

        [Header("UI References")]
        [SerializeField] private UIManager uiManager;

        // Game State
        public static GameManager Instance { get; private set; }
        public GameState CurrentState { get; private set; }
        
        // Game Variables
        public int Lives { get; private set; }
        public int Currency { get; private set; }
        public int CurrentWave { get; private set; }
        public int EnemiesRemaining { get; private set; }

        // Events
        public System.Action<int> OnLivesChanged;
        public System.Action<int> OnCurrencyChanged;
        public System.Action<int> OnWaveChanged;
        public System.Action<GameState> OnGameStateChanged;

        private WaveSpawner waveSpawner;

        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            InitializeGame();
        }

        private void Start()
        {
            waveSpawner = FindObjectOfType<WaveSpawner>();
            if (uiManager == null)
                uiManager = FindObjectOfType<UIManager>();

            StartGame();
        }

        private void InitializeGame()
        {
            Lives = startingLives;
            Currency = startingCurrency;
            CurrentWave = 0;
            EnemiesRemaining = 0;
            CurrentState = GameState.Start;
        }

        public void StartGame()
        {
            ChangeGameState(GameState.Playing);
            
            // Notify UI of initial values
            OnLivesChanged?.Invoke(Lives);
            OnCurrencyChanged?.Invoke(Currency);
            OnWaveChanged?.Invoke(CurrentWave);
        }

        public void PauseGame()
        {
            if (CurrentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
                Time.timeScale = 0f;
            }
        }

        public void ResumeGame()
        {
            if (CurrentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
                Time.timeScale = 1f;
            }
        }

        public void GameOver()
        {
            ChangeGameState(GameState.GameOver);
            StartCoroutine(GameOverSequence());
        }

        public void Victory()
        {
            ChangeGameState(GameState.Victory);
            // Handle victory logic (unlock next level, etc.)
        }

        private IEnumerator GameOverSequence()
        {
            yield return new WaitForSeconds(gameOverDelay);
            // Show game over UI or restart level
            uiManager?.ShowGameOverScreen();
        }

        public void LoseLife()
        {
            Lives--;
            OnLivesChanged?.Invoke(Lives);

            if (Lives <= 0)
            {
                GameOver();
            }
        }

        public void AddCurrency(int amount)
        {
            Currency += amount;
            OnCurrencyChanged?.Invoke(Currency);
        }

        public bool SpendCurrency(int amount)
        {
            if (Currency >= amount)
            {
                Currency -= amount;
                OnCurrencyChanged?.Invoke(Currency);
                return true;
            }
            return false;
        }

        public void StartNextWave()
        {
            if (CurrentState == GameState.Playing && waveSpawner != null)
            {
                CurrentWave++;
                OnWaveChanged?.Invoke(CurrentWave);
                waveSpawner.StartWave();
            }
        }

        public void SetEnemiesRemaining(int count)
        {
            EnemiesRemaining = count;
            
            // Check for wave completion
            if (EnemiesRemaining <= 0 && CurrentState == GameState.Playing)
            {
                OnWaveComplete();
            }
        }

        private void OnWaveComplete()
        {
            // Check if this was the final wave
            if (waveSpawner != null && waveSpawner.IsLastWave())
            {
                Victory();
            }
        }

        private void ChangeGameState(GameState newState)
        {
            CurrentState = newState;
            OnGameStateChanged?.Invoke(CurrentState);
        }

        public void RestartLevel()
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene(SceneManager.GetActiveScene().name);
        }

        public void LoadMainMenu()
        {
            Time.timeScale = 1f;
            SceneManager.LoadScene("MainMenu");
        }

        public void LoadNextLevel()
        {
            Time.timeScale = 1f;
            // Load next level based on current scene
            int nextSceneIndex = SceneManager.GetActiveScene().buildIndex + 1;
            if (nextSceneIndex < SceneManager.sceneCountInBuildSettings)
            {
                SceneManager.LoadScene(nextSceneIndex);
            }
            else
            {
                LoadMainMenu(); // No more levels, return to main menu
            }
        }

        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
            }
        }
    }
}
