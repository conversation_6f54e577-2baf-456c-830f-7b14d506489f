using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace TowerDefense.Towers
{
    public enum TowerType
    {
        Basic,
        Rapid,
        Heavy,
        Laser
    }

    public class Tower : MonoBehaviour
    {
        [Header("Tower Settings")]
        [SerializeField] private TowerType towerType = TowerType.Basic;
        [SerializeField] private float range = 5f;
        [SerializeField] private float fireRate = 1f;
        [SerializeField] private int damage = 25;
        [SerializeField] private int cost = 50;

        [Header("Projectile Settings")]
        [SerializeField] private GameObject projectilePrefab;
        [SerializeField] private Transform firePoint;
        [SerializeField] private bool useRaycast = false;

        [Header("Visual Settings")]
        [SerializeField] private Transform turretHead;
        [SerializeField] private float rotationSpeed = 5f;
        [SerializeField] private LineRenderer rangeIndicator;

        [Header("Audio")]
        [SerializeField] private AudioClip shootSound;

        // Private variables
        private Enemy targetEnemy;
        private float fireCountdown = 0f;
        private List<Enemy> enemiesInRange = new List<Enemy>();
        private AudioSource audioSource;
        private bool isSelected = false;

        // Tower stats (can be upgraded)
        public float Range { get; private set; }
        public float FireRate { get; private set; }
        public int Damage { get; private set; }
        public int Cost { get; private set; }
        public TowerType Type { get; private set; }

        // Events
        public System.Action<Tower> OnTowerSelected;
        public System.Action<Enemy> OnEnemyHit;

        private void Start()
        {
            InitializeTower();
            SetupComponents();
        }

        private void InitializeTower()
        {
            Range = range;
            FireRate = fireRate;
            Damage = damage;
            Cost = cost;
            Type = towerType;
        }

        private void SetupComponents()
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            if (rangeIndicator != null)
            {
                rangeIndicator.enabled = false;
                SetupRangeIndicator();
            }

            if (firePoint == null && transform.childCount > 0)
            {
                firePoint = transform.GetChild(0);
            }
        }

        private void Update()
        {
            if (GameManager.Instance?.CurrentState != Core.GameState.Playing)
                return;

            UpdateTarget();
            
            if (targetEnemy != null)
            {
                RotateTowardsTarget();
                
                if (fireCountdown <= 0f)
                {
                    Shoot();
                    fireCountdown = 1f / FireRate;
                }
            }

            fireCountdown -= Time.deltaTime;
        }

        private void UpdateTarget()
        {
            // Remove dead or out-of-range enemies
            enemiesInRange.RemoveAll(enemy => enemy == null || 
                Vector3.Distance(transform.position, enemy.transform.position) > Range);

            // Find new target if current target is invalid
            if (targetEnemy == null || !enemiesInRange.Contains(targetEnemy))
            {
                targetEnemy = GetClosestEnemy();
            }
        }

        private Enemy GetClosestEnemy()
        {
            Enemy closest = null;
            float shortestDistance = Mathf.Infinity;

            foreach (Enemy enemy in enemiesInRange)
            {
                if (enemy != null)
                {
                    float distance = Vector3.Distance(transform.position, enemy.transform.position);
                    if (distance < shortestDistance)
                    {
                        shortestDistance = distance;
                        closest = enemy;
                    }
                }
            }

            return closest;
        }

        private void RotateTowardsTarget()
        {
            if (targetEnemy == null || turretHead == null)
                return;

            Vector3 direction = (targetEnemy.transform.position - turretHead.position).normalized;
            Quaternion lookRotation = Quaternion.LookRotation(direction);
            turretHead.rotation = Quaternion.Slerp(turretHead.rotation, lookRotation, rotationSpeed * Time.deltaTime);
        }

        private void Shoot()
        {
            if (targetEnemy == null)
                return;

            if (useRaycast)
            {
                ShootRaycast();
            }
            else
            {
                ShootProjectile();
            }

            PlayShootSound();
        }

        private void ShootRaycast()
        {
            // Instant hit with raycast
            targetEnemy.TakeDamage(Damage);
            OnEnemyHit?.Invoke(targetEnemy);

            // Visual effect (line renderer, particle effect, etc.)
            if (firePoint != null)
            {
                Debug.DrawLine(firePoint.position, targetEnemy.transform.position, Color.red, 0.1f);
            }
        }

        private void ShootProjectile()
        {
            if (projectilePrefab == null || firePoint == null)
                return;

            GameObject projectileObject = Instantiate(projectilePrefab, firePoint.position, firePoint.rotation);
            Projectile projectile = projectileObject.GetComponent<Projectile>();
            
            if (projectile != null)
            {
                projectile.Initialize(targetEnemy, Damage);
                projectile.OnHit += OnProjectileHit;
            }
        }

        private void OnProjectileHit(Enemy enemy, int damage)
        {
            OnEnemyHit?.Invoke(enemy);
        }

        private void PlayShootSound()
        {
            if (audioSource != null && shootSound != null)
            {
                audioSource.PlayOneShot(shootSound);
            }
        }

        private void OnTriggerEnter(Collider other)
        {
            Enemy enemy = other.GetComponent<Enemy>();
            if (enemy != null && !enemiesInRange.Contains(enemy))
            {
                enemiesInRange.Add(enemy);
            }
        }

        private void OnTriggerExit(Collider other)
        {
            Enemy enemy = other.GetComponent<Enemy>();
            if (enemy != null && enemiesInRange.Contains(enemy))
            {
                enemiesInRange.Remove(enemy);
                
                if (targetEnemy == enemy)
                {
                    targetEnemy = null;
                }
            }
        }

        public void SelectTower()
        {
            isSelected = true;
            ShowRangeIndicator(true);
            OnTowerSelected?.Invoke(this);
        }

        public void DeselectTower()
        {
            isSelected = false;
            ShowRangeIndicator(false);
        }

        private void ShowRangeIndicator(bool show)
        {
            if (rangeIndicator != null)
            {
                rangeIndicator.enabled = show;
            }
        }

        private void SetupRangeIndicator()
        {
            if (rangeIndicator == null)
                return;

            int segments = 64;
            rangeIndicator.positionCount = segments + 1;
            rangeIndicator.useWorldSpace = false;
            rangeIndicator.loop = true;

            float angle = 0f;
            for (int i = 0; i <= segments; i++)
            {
                float x = Mathf.Sin(Mathf.Deg2Rad * angle) * Range;
                float z = Mathf.Cos(Mathf.Deg2Rad * angle) * Range;
                rangeIndicator.SetPosition(i, new Vector3(x, 0.1f, z));
                angle += 360f / segments;
            }
        }

        public void UpgradeTower(float rangeMultiplier, float fireRateMultiplier, int damageIncrease)
        {
            Range *= rangeMultiplier;
            FireRate *= fireRateMultiplier;
            Damage += damageIncrease;

            // Update range indicator
            if (rangeIndicator != null)
            {
                SetupRangeIndicator();
            }

            // Update trigger collider
            SphereCollider triggerCollider = GetComponent<SphereCollider>();
            if (triggerCollider != null)
            {
                triggerCollider.radius = Range;
            }
        }

        private void OnDrawGizmosSelected()
        {
            // Draw range in scene view
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, Range);
        }
    }
}
