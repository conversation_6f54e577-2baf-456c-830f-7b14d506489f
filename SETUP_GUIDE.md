# Tower Defense Game - Unity Setup Guide

This guide will help you set up the Tower Defense game in Unity following the instructions.md specifications.

## Prerequisites

1. **Unity Hub** - Latest version
2. **Unity Editor** - 2022.3 LTS or newer with:
   - Android Build Support
   - iOS Build Support (macOS only)
3. **Visual Studio Code** with Unity Debugger Extension
4. **Xcode** (macOS only, for iOS builds)

## Project Setup

### 1. Create New Unity Project

1. Open Unity Hub
2. Click "New Project"
3. Select "3D Core" template (URP recommended)
4. Name your project "TowerDefense"
5. Choose project location
6. Click "Create Project"

### 2. Import Scripts

1. Copy the entire `Assets/Scripts` folder to your Unity project's Assets folder
2. Unity will automatically compile the scripts

### 3. Configure Project Settings

#### Build Settings
1. Go to `File > Build Settings`
2. Add scenes to build:
   - MainMenu (index 0)
   - Level1 (index 1)
3. Switch platform to Android or iOS as needed

#### Player Settings
1. Go to `Edit > Project Settings > Player`
2. Set appropriate settings for mobile:
   - Company Name
   - Product Name
   - Bundle Identifier
   - Version
   - Minimum API Level (Android)
   - Target SDK Version (Android)

#### Input System
1. Install Input System package if not already installed
2. Go to `Edit > Project Settings > XR Plug-in Management > Input`
3. Enable "Input System Package"

### 4. Scene Setup

#### MainMenu Scene
1. Create new scene: `File > New Scene`
2. Save as "MainMenu"
3. Add Canvas with UI elements:
   - Main menu buttons
   - Level selection panel
   - Settings panel
4. Add MainMenu script to a GameObject
5. Configure UI references in the script

#### Level1 Scene
1. Create new scene: `File > New Scene`
2. Save as "Level1"
3. Set up the level:
   - Create terrain or level geometry
   - Add waypoint path for enemies
   - Place build zones for towers
   - Add spawn point for enemies

### 5. Create Essential GameObjects

#### GameManager
1. Create empty GameObject named "GameManager"
2. Add GameManager script
3. Configure starting values (lives, currency, etc.)

#### WaveSpawner
1. Create empty GameObject named "WaveSpawner"
2. Add WaveSpawner script
3. Configure wave data
4. Set spawn point reference

#### UIManager
1. Create Canvas if not exists
2. Add UIManager script to Canvas
3. Create UI elements:
   - HUD (lives, currency, wave counter)
   - Tower selection buttons
   - Pause menu
   - Game over screen

#### AudioManager
1. Create empty GameObject named "AudioManager"
2. Add AudioManager script
3. Configure sound effects and music
4. Set up AudioMixer for volume control

### 6. Create Prefabs

#### Tower Prefabs
1. Create 3D models or use primitives for towers
2. Add Tower script
3. Configure stats (range, damage, fire rate, cost)
4. Add SphereCollider for range detection
5. Save as prefabs in Assets/Prefabs/Towers/

#### Enemy Prefabs
1. Create 3D models or use primitives for enemies
2. Add Enemy script
3. Add NavMeshAgent component
4. Configure stats (health, speed, reward)
5. Save as prefabs in Assets/Prefabs/Enemies/

#### Projectile Prefabs
1. Create simple 3D model for projectiles
2. Add Projectile script
3. Add Rigidbody and Collider
4. Save as prefabs in Assets/Prefabs/Projectiles/

### 7. Configure Layers

1. Go to `Edit > Project Settings > Tags and Layers`
2. Add custom layers:
   - Buildable (for build zones)
   - Tower (for tower objects)
   - Enemy (for enemy objects)
   - Projectile (for projectiles)

### 8. Set up NavMesh

1. Select level geometry
2. Mark as "Navigation Static" in Inspector
3. Go to `Window > AI > Navigation`
4. Click "Bake" to generate NavMesh

### 9. Create ScriptableObject Assets

#### Tower Data
1. Right-click in Project window
2. Create > Tower Defense > Tower Data
3. Configure tower stats and references
4. Create one for each tower type

#### Enemy Data
1. Right-click in Project window
2. Create > Tower Defense > Enemy Data
3. Configure enemy stats and references
4. Create one for each enemy type

#### Wave Data
1. Right-click in Project window
2. Create > Tower Defense > Wave Data
3. Configure wave composition and timing
4. Create one for each wave

#### Level Data
1. Right-click in Project window
2. Create > Tower Defense > Level Data
3. Configure level settings and wave references
4. Create one for each level

### 10. Mobile Optimization

1. Add MobileOptimizer script to a GameObject in each scene
2. Configure quality settings for target devices
3. Set up appropriate texture compression
4. Configure lighting settings for mobile

### 11. Audio Setup

1. Create AudioMixer asset
2. Set up groups for Music and SFX
3. Configure AudioManager with sound clips
4. Test volume controls

### 12. Testing

1. Test in Unity Editor first
2. Build and test on actual mobile devices
3. Check performance with Unity Profiler
4. Verify touch controls work correctly

## Common Issues and Solutions

### Scripts Not Compiling
- Check for missing using statements
- Ensure all script files are in correct folders
- Check Unity Console for specific errors

### Touch Input Not Working
- Verify EventSystem exists in scene
- Check if UI elements are blocking raycasts
- Ensure touch input script is properly configured

### NavMesh Issues
- Rebake NavMesh after level changes
- Check if enemies have NavMeshAgent component
- Verify waypoints are on NavMesh surface

### Performance Issues
- Use Unity Profiler to identify bottlenecks
- Reduce draw calls with batching
- Optimize texture sizes for mobile
- Use object pooling for frequently spawned objects

## Next Steps

1. Create art assets or import from Asset Store
2. Add particle effects for visual polish
3. Implement save/load system
4. Add more tower and enemy types
5. Create additional levels
6. Implement achievement system
7. Add monetization (if desired)
8. Prepare for app store submission

## Resources

- [Unity Manual](https://docs.unity3d.com/Manual/)
- [Unity Mobile Optimization](https://docs.unity3d.com/Manual/MobileOptimization.html)
- [Unity Input System](https://docs.unity3d.com/Packages/com.unity.inputsystem@1.0/manual/)
- [Unity NavMesh](https://docs.unity3d.com/Manual/nav-NavigationSystem.html)
- [Unity Audio](https://docs.unity3d.com/Manual/Audio.html)
