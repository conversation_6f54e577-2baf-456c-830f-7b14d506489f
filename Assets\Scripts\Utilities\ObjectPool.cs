using UnityEngine;
using System.Collections.Generic;

namespace TowerDefense.Utilities
{
    public class ObjectPool : MonoBehaviour
    {
        [Head<PERSON>("Pool Settings")]
        [SerializeField] private GameObject prefab;
        [SerializeField] private int initialPoolSize = 10;
        [SerializeField] private int maxPoolSize = 50;
        [SerializeField] private bool expandPool = true;

        private Queue<GameObject> pool = new Queue<GameObject>();
        private List<GameObject> activeObjects = new List<GameObject>();

        private void Start()
        {
            InitializePool();
        }

        private void InitializePool()
        {
            if (prefab == null)
            {
                Debug.LogError("ObjectPool: No prefab assigned!");
                return;
            }

            for (int i = 0; i < initialPoolSize; i++)
            {
                CreatePooledObject();
            }
        }

        private GameObject CreatePooledObject()
        {
            GameObject obj = Instantiate(prefab, transform);
            obj.SetActive(false);
            pool.Enqueue(obj);
            return obj;
        }

        public GameObject GetPooledObject()
        {
            GameObject obj = null;

            if (pool.Count > 0)
            {
                obj = pool.Dequeue();
            }
            else if (expandPool && activeObjects.Count < maxPoolSize)
            {
                obj = CreatePooledObject();
                pool.Dequeue(); // Remove it from pool since we're about to use it
            }

            if (obj != null)
            {
                obj.SetActive(true);
                activeObjects.Add(obj);
            }

            return obj;
        }

        public void ReturnToPool(GameObject obj)
        {
            if (obj == null)
                return;

            if (activeObjects.Contains(obj))
            {
                activeObjects.Remove(obj);
                obj.SetActive(false);
                obj.transform.SetParent(transform);
                pool.Enqueue(obj);
            }
        }

        public void ReturnAllToPool()
        {
            for (int i = activeObjects.Count - 1; i >= 0; i--)
            {
                ReturnToPool(activeObjects[i]);
            }
        }

        public int GetActiveCount()
        {
            return activeObjects.Count;
        }

        public int GetPooledCount()
        {
            return pool.Count;
        }

        public int GetTotalCount()
        {
            return GetActiveCount() + GetPooledCount();
        }

        private void OnDestroy()
        {
            ReturnAllToPool();
        }
    }
}
