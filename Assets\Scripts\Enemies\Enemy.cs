using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace TowerDefense.Enemies
{
    public enum EnemyType
    {
        Basic,
        Fast,
        Heavy,
        Flying
    }

    public class Enemy : MonoBehaviour
    {
        [Header("Enemy Settings")]
        [SerializeField] private EnemyType enemyType = EnemyType.Basic;
        [SerializeField] private int maxHealth = 100;
        [SerializeField] private float moveSpeed = 3f;
        [SerializeField] private int rewardValue = 10;

        [Head<PERSON>("Visual Settings")]
        [SerializeField] private GameObject healthBarPrefab;
        [SerializeField] private Transform healthBarParent;
        [SerializeField] private Renderer enemyRenderer;

        [Header("Audio")]
        [SerializeField] private AudioClip deathSound;
        [SerializeField] private AudioClip hitSound;

        [Header("Effects")]
        [SerializeField] private GameObject deathEffect;
        [SerializeField] private GameObject hitEffect;

        // Private variables
        private int currentHealth;
        private NavMeshAgent navAgent;
        private Waypoint[] waypoints;
        private int currentWaypointIndex = 0;
        private AudioSource audioSource;
        private HealthBar healthBar;
        private bool isDead = false;
        private bool hasReachedEnd = false;

        // Properties
        public int CurrentHealth => currentHealth;
        public int MaxHealth => maxHealth;
        public float HealthPercentage => (float)currentHealth / maxHealth;
        public EnemyType Type => enemyType;
        public int RewardValue => rewardValue;
        public bool IsDead => isDead;

        // Events
        public System.Action<Enemy, int> OnEnemyDeath;
        public System.Action<Enemy> OnEnemyReachedEnd;
        public System.Action<Enemy, int> OnEnemyTakeDamage;

        private void Start()
        {
            InitializeEnemy();
            SetupComponents();
            FindPath();
        }

        private void InitializeEnemy()
        {
            currentHealth = maxHealth;
        }

        private void SetupComponents()
        {
            // Setup NavMesh Agent
            navAgent = GetComponent<NavMeshAgent>();
            if (navAgent == null)
                navAgent = gameObject.AddComponent<NavMeshAgent>();
            
            navAgent.speed = moveSpeed;
            navAgent.stoppingDistance = 0.1f;

            // Setup Audio
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
                audioSource = gameObject.AddComponent<AudioSource>();

            // Setup Health Bar
            if (healthBarPrefab != null)
            {
                GameObject healthBarObject = Instantiate(healthBarPrefab, healthBarParent);
                healthBar = healthBarObject.GetComponent<HealthBar>();
                if (healthBar != null)
                {
                    healthBar.Initialize(this);
                }
            }

            // Setup Renderer
            if (enemyRenderer == null)
                enemyRenderer = GetComponent<Renderer>();
        }

        private void FindPath()
        {
            // Find waypoints in the scene
            GameObject waypointContainer = GameObject.FindGameObjectWithTag("Waypoints");
            if (waypointContainer != null)
            {
                waypoints = waypointContainer.GetComponentsInChildren<Waypoint>();
                
                if (waypoints.Length > 0)
                {
                    MoveToNextWaypoint();
                }
                else
                {
                    Debug.LogError("Enemy: No waypoints found!");
                }
            }
            else
            {
                Debug.LogError("Enemy: No waypoint container found! Make sure to tag waypoint container with 'Waypoints'");
            }
        }

        private void Update()
        {
            if (isDead || hasReachedEnd)
                return;

            CheckWaypointReached();
        }

        private void CheckWaypointReached()
        {
            if (navAgent == null || waypoints == null || currentWaypointIndex >= waypoints.Length)
                return;

            if (!navAgent.pathPending && navAgent.remainingDistance < 0.5f)
            {
                currentWaypointIndex++;
                
                if (currentWaypointIndex >= waypoints.Length)
                {
                    ReachEnd();
                }
                else
                {
                    MoveToNextWaypoint();
                }
            }
        }

        private void MoveToNextWaypoint()
        {
            if (waypoints != null && currentWaypointIndex < waypoints.Length)
            {
                navAgent.SetDestination(waypoints[currentWaypointIndex].transform.position);
            }
        }

        public void TakeDamage(int damage)
        {
            if (isDead)
                return;

            currentHealth -= damage;
            currentHealth = Mathf.Max(0, currentHealth);

            OnEnemyTakeDamage?.Invoke(this, damage);

            // Update health bar
            if (healthBar != null)
            {
                healthBar.UpdateHealth();
            }

            // Play hit effects
            PlayHitEffects();

            // Check if dead
            if (currentHealth <= 0)
            {
                Die();
            }
        }

        private void PlayHitEffects()
        {
            // Play hit sound
            if (audioSource != null && hitSound != null)
            {
                audioSource.PlayOneShot(hitSound);
            }

            // Spawn hit effect
            if (hitEffect != null)
            {
                GameObject effect = Instantiate(hitEffect, transform.position, Quaternion.identity);
                Destroy(effect, 1f);
            }

            // Flash red (optional visual feedback)
            StartCoroutine(FlashRed());
        }

        private IEnumerator FlashRed()
        {
            if (enemyRenderer != null)
            {
                Color originalColor = enemyRenderer.material.color;
                enemyRenderer.material.color = Color.red;
                yield return new WaitForSeconds(0.1f);
                enemyRenderer.material.color = originalColor;
            }
        }

        private void Die()
        {
            if (isDead)
                return;

            isDead = true;

            // Stop movement
            if (navAgent != null)
            {
                navAgent.isStopped = true;
            }

            // Play death effects
            PlayDeathEffects();

            // Notify listeners
            OnEnemyDeath?.Invoke(this, rewardValue);

            // Destroy enemy after a short delay
            StartCoroutine(DestroyAfterDelay(1f));
        }

        private void PlayDeathEffects()
        {
            // Play death sound
            if (audioSource != null && deathSound != null)
            {
                audioSource.PlayOneShot(deathSound);
            }

            // Spawn death effect
            if (deathEffect != null)
            {
                GameObject effect = Instantiate(deathEffect, transform.position, Quaternion.identity);
                Destroy(effect, 2f);
            }
        }

        private void ReachEnd()
        {
            if (hasReachedEnd || isDead)
                return;

            hasReachedEnd = true;

            // Stop movement
            if (navAgent != null)
            {
                navAgent.isStopped = true;
            }

            // Notify listeners
            OnEnemyReachedEnd?.Invoke(this);

            // Destroy enemy
            StartCoroutine(DestroyAfterDelay(0.5f));
        }

        private IEnumerator DestroyAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            
            // Hide visual components but keep audio playing if needed
            if (enemyRenderer != null)
                enemyRenderer.enabled = false;
            
            if (healthBar != null)
                healthBar.gameObject.SetActive(false);

            // Wait a bit more for audio to finish
            yield return new WaitForSeconds(0.5f);
            
            Destroy(gameObject);
        }

        public void SetSpeed(float newSpeed)
        {
            moveSpeed = newSpeed;
            if (navAgent != null)
            {
                navAgent.speed = moveSpeed;
            }
        }

        public void SetHealth(int newMaxHealth)
        {
            maxHealth = newMaxHealth;
            currentHealth = maxHealth;
            
            if (healthBar != null)
            {
                healthBar.UpdateHealth();
            }
        }

        public void SetReward(int newReward)
        {
            rewardValue = newReward;
        }

        private void OnDestroy()
        {
            // Clean up events
            OnEnemyDeath = null;
            OnEnemyReachedEnd = null;
            OnEnemyTakeDamage = null;
        }
    }
}
