
# 📦 instructions.md  
**Project:** 3D Space Tower Defense (Mobile)  
**Platforms:** Android & iOS  
**Engine:** Unity (C#)  
**Style:** StarCraft-like 3D, single-player  

---

## ✅ 1. Environment Setup

### Install
- [Unity Hub](https://unity.com/download): Latest LTS + Android/iOS Build Support  
- [VS Code](https://code.visualstudio.com) + Unity Debugger Extension  
- [Xcode](https://developer.apple.com/xcode/) (macOS only, for iOS builds)

### Configure
- In Unity:  
  - External Script Editor → VS Code  
  - Enable: Android & iOS platforms in Build Settings  
- Mobile SDKs auto-installed via Unity Hub  
- Version Control: `.gitignore` `Library/`, `obj/`, etc.

---

## 🧱 2. Project Setup

```bash
Unity Hub → New Project → 3D Core (URP or HDRP)
```

### Structure
```
Assets/
├── Art/
├── Audio/
├── Prefabs/
├── Scenes/
├── Scripts/
├── UI/
```

### Initial Scenes
- `MainMenu`, `Level1`  
- Set mobile resolution (16:9, 18:9)  
- Add scenes in Build Settings

---

## 🎮 3. Core Gameplay Loop

- **GameManager.cs**: State (Start, Pause, GameOver), Currency, Lives  
- **WaveSpawner.cs**: Coroutine spawns enemies over time  
- End level: all enemies dead or player defeated

---

## 🎯 4. Touch UI (Mobile)

- Use **Canvas** with scale mode set to *Scale with Screen Size*  
- Raycast touch input for tower placement  
- Mobile HUD: wave counter, money, pause, lives  
- Use Unity UI Buttons for tower selection & upgrades

---

## 🌌 5. Level & Progression

- Create 3D levels with build zones & enemy paths  
- Use waypoints or NavMesh for enemy movement  
- Progression: unlock next level on success  
- Scale difficulty (enemy HP, speed, spawn rate)

---

## 🧊 6. Assets & Animation

- Start with Unity primitives or free assets  
- Later: replace with FBX/OBJ sci-fi models (low-poly preferred)  
- Animate enemies via Animator  
- Optimize with baked lighting, LODs, and mobile shaders

---

## 🛠 7. Mechanics

- **Tower.cs**: Detect enemies in range, shoot (raycast/projectiles)  
- **Enemy.cs**: Follow path, take damage, die (reward player)  
- **Upgrade.cs**: Tap tower → upgrade UI → enhance stats  
- Use `ScriptableObjects` or serialized fields for balance tuning

---

## 🔊 8. Audio

- Add `AudioSource` on camera (BGM) & GameObjects (SFX)  
- `AudioMixer` for Music/SFX control  
- Play via `PlayOneShot()` for shooting, death, clicks  
- Use compressed OGG files for mobile

---

## 🧪 9. Testing

- Use Unity Test Framework (Edit & PlayMode)  
- Regularly test on real Android/iOS devices  
- Checklist:
  - Touch controls
  - Wave spawning
  - UI layout
  - Game loop  
- Use Unity Profiler for performance tuning

---

## 🚀 10. Deployment

### Android
- Build Settings → Android → APK or AAB  
- Add keystore for release  
- Test on device & upload to Google Play Console

### iOS (macOS only)
- Build Settings → iOS → Export Xcode project  
- Open in Xcode → configure Team/Profile  
- Build/Archive → App Store Connect

---

## 📚 Resources

- Unity Manual: [Android](https://docs.unity3d.com/Manual/android-BuildProcess.html) | [iOS](https://docs.unity3d.com/Manual/iphone-BuildProcess.html)  
- Input System: [Touch Docs](https://docs.unity3d.com/Packages/com.unity.inputsystem@1.0/manual/Touch.html)  
- Unity Asset Store: [Sci-Fi Models](https://assetstore.unity.com)  
- Testing: [Unity Test Framework](https://docs.unity3d.com/Packages/com.unity.test-framework@1.1/manual/index.html)
