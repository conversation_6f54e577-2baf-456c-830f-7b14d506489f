using UnityEngine;

namespace TowerDefense.Towers
{
    public class TowerUpgrade : MonoBehaviour
    {
        [Header("Upgrade Settings")]
        [SerializeField] private Data.TowerData towerData;
        [SerializeField] private int currentUpgradeLevel = 0;
        [SerializeField] private int maxUpgradeLevel = 3;

        private Tower tower;

        // Events
        public System.Action<int> OnTowerUpgraded;
        public System.Action<int> OnMaxUpgradeReached;

        private void Start()
        {
            tower = GetComponent<Tower>();
            if (tower == null)
            {
                Debug.LogError("TowerUpgrade: No Tower component found!");
            }
        }

        public bool CanUpgrade()
        {
            return currentUpgradeLevel < maxUpgradeLevel && 
                   currentUpgradeLevel < GetMaxAvailableUpgrades();
        }

        public int GetUpgradeCost()
        {
            if (!CanUpgrade() || towerData == null)
                return 0;

            if (currentUpgradeLevel < towerData.upgrades.Length)
            {
                return towerData.upgrades[currentUpgradeLevel].cost;
            }

            // Default cost calculation if no specific upgrade data
            return Mathf.RoundToInt(tower.Cost * 0.5f * (currentUpgradeLevel + 1));
        }

        public bool TryUpgrade()
        {
            if (!CanUpgrade())
                return false;

            int cost = GetUpgradeCost();
            
            if (Core.GameManager.Instance != null && Core.GameManager.Instance.SpendCurrency(cost))
            {
                PerformUpgrade();
                return true;
            }

            return false;
        }

        private void PerformUpgrade()
        {
            if (towerData != null && currentUpgradeLevel < towerData.upgrades.Length)
            {
                // Use specific upgrade data
                Data.TowerUpgrade upgradeData = towerData.upgrades[currentUpgradeLevel];
                tower.UpgradeTower(upgradeData.rangeMultiplier, 
                                 upgradeData.fireRateMultiplier, 
                                 upgradeData.damageIncrease);
            }
            else
            {
                // Use default upgrade values
                float rangeMultiplier = 1.1f + (currentUpgradeLevel * 0.05f);
                float fireRateMultiplier = 1.1f + (currentUpgradeLevel * 0.05f);
                int damageIncrease = 5 + (currentUpgradeLevel * 5);
                
                tower.UpgradeTower(rangeMultiplier, fireRateMultiplier, damageIncrease);
            }

            currentUpgradeLevel++;
            OnTowerUpgraded?.Invoke(currentUpgradeLevel);

            // Play upgrade effects
            PlayUpgradeEffects();

            if (currentUpgradeLevel >= maxUpgradeLevel)
            {
                OnMaxUpgradeReached?.Invoke(currentUpgradeLevel);
            }
        }

        private void PlayUpgradeEffects()
        {
            // Play upgrade sound
            if (towerData != null && towerData.upgradeSound != null)
            {
                Audio.AudioManager.Instance?.PlaySFX("TowerUpgrade");
            }

            // Visual upgrade effect (particles, etc.)
            // This would be implemented with particle systems or other visual effects
        }

        public int GetCurrentUpgradeLevel()
        {
            return currentUpgradeLevel;
        }

        public int GetMaxUpgradeLevel()
        {
            return maxUpgradeLevel;
        }

        private int GetMaxAvailableUpgrades()
        {
            if (towerData != null && towerData.upgrades != null)
            {
                return Mathf.Min(maxUpgradeLevel, towerData.upgrades.Length);
            }
            return maxUpgradeLevel;
        }

        public string GetUpgradeDescription()
        {
            if (towerData != null && currentUpgradeLevel < towerData.upgrades.Length)
            {
                return towerData.upgrades[currentUpgradeLevel].description;
            }

            return $"Upgrade Level {currentUpgradeLevel + 1}";
        }

        public string GetCurrentUpgradeInfo()
        {
            if (!CanUpgrade())
                return "Max Level Reached";

            string info = $"Upgrade to Level {currentUpgradeLevel + 1}\n";
            info += $"Cost: ${GetUpgradeCost()}\n";
            info += GetUpgradeDescription();

            return info;
        }

        public float GetUpgradeProgress()
        {
            return (float)currentUpgradeLevel / maxUpgradeLevel;
        }

        public void SetTowerData(Data.TowerData data)
        {
            towerData = data;
        }

        public Data.TowerData GetTowerData()
        {
            return towerData;
        }
    }
}
