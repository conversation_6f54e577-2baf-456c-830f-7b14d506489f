using UnityEngine;

namespace TowerDefense.Utilities
{
    public class MobileOptimizer : Mono<PERSON>ehaviour
    {
        [Header("Performance Settings")]
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableVSync = false;
        [SerializeField] private bool optimizeForBattery = false;

        [Header("Quality Settings")]
        [SerializeField] private bool autoDetectQuality = true;
        [SerializeField] private int forcedQualityLevel = -1;

        [Header("Memory Management")]
        [SerializeField] private bool enableMemoryOptimization = true;
        [SerializeField] private float memoryCleanupInterval = 30f;

        private float lastMemoryCleanup;

        private void Start()
        {
            ApplyMobileOptimizations();
        }

        private void Update()
        {
            if (enableMemoryOptimization)
            {
                HandleMemoryManagement();
            }
        }

        private void ApplyMobileOptimizations()
        {
            // Set target frame rate
            Application.targetFrameRate = targetFrameRate;

            // Configure VSync
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;

            // Optimize for battery if requested
            if (optimizeForBattery)
            {
                Application.targetFrameRate = 30;
                QualitySettings.vSyncCount = 0;
            }

            // Auto-detect quality settings based on device
            if (autoDetectQuality)
            {
                DetectAndSetQuality();
            }
            else if (forcedQualityLevel >= 0)
            {
                QualitySettings.SetQualityLevel(forcedQualityLevel);
            }

            // Mobile-specific settings
            SetMobileSpecificSettings();
        }

        private void DetectAndSetQuality()
        {
            // Simple device detection based on memory and processor
            int systemMemoryMB = SystemInfo.systemMemorySize;
            int processorCount = SystemInfo.processorCount;

            int qualityLevel = 0; // Default to lowest

            if (systemMemoryMB >= 4096 && processorCount >= 4)
            {
                qualityLevel = 2; // High quality
            }
            else if (systemMemoryMB >= 2048 && processorCount >= 2)
            {
                qualityLevel = 1; // Medium quality
            }

            // Ensure quality level is within bounds
            qualityLevel = Mathf.Clamp(qualityLevel, 0, QualitySettings.names.Length - 1);
            QualitySettings.SetQualityLevel(qualityLevel);

            Debug.Log($"Auto-detected quality level: {QualitySettings.names[qualityLevel]}");
        }

        private void SetMobileSpecificSettings()
        {
            // Disable unnecessary features for mobile
            QualitySettings.realtimeReflectionProbes = false;
            QualitySettings.billboardsFaceCameraPosition = false;

            // Optimize shadow settings
            QualitySettings.shadowCascades = 1;
            QualitySettings.shadowDistance = 50f;

            // Optimize texture settings
            QualitySettings.masterTextureLimit = 0;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;

            // Optimize particle settings
            QualitySettings.particleRaycastBudget = 64;

            // Set appropriate pixel light count
            QualitySettings.pixelLightCount = 1;
        }

        private void HandleMemoryManagement()
        {
            if (Time.time - lastMemoryCleanup > memoryCleanupInterval)
            {
                lastMemoryCleanup = Time.time;
                
                // Force garbage collection
                System.GC.Collect();
                
                // Unload unused assets
                Resources.UnloadUnusedAssets();
                
                Debug.Log("Performed memory cleanup");
            }
        }

        public void SetTargetFrameRate(int frameRate)
        {
            targetFrameRate = frameRate;
            Application.targetFrameRate = targetFrameRate;
        }

        public void SetQualityLevel(int level)
        {
            if (level >= 0 && level < QualitySettings.names.Length)
            {
                QualitySettings.SetQualityLevel(level);
                forcedQualityLevel = level;
                autoDetectQuality = false;
            }
        }

        public void EnableBatteryOptimization(bool enable)
        {
            optimizeForBattery = enable;
            
            if (enable)
            {
                SetTargetFrameRate(30);
                QualitySettings.vSyncCount = 0;
                QualitySettings.SetQualityLevel(0); // Lowest quality
            }
            else
            {
                SetTargetFrameRate(60);
                if (autoDetectQuality)
                {
                    DetectAndSetQuality();
                }
            }
        }

        public void ForceMemoryCleanup()
        {
            System.GC.Collect();
            Resources.UnloadUnusedAssets();
            lastMemoryCleanup = Time.time;
        }

        // Static utility methods
        public static bool IsMobilePlatform()
        {
            return Application.isMobilePlatform;
        }

        public static bool IsLowEndDevice()
        {
            return SystemInfo.systemMemorySize < 2048 || SystemInfo.processorCount < 2;
        }

        public static string GetDeviceInfo()
        {
            return $"Device: {SystemInfo.deviceModel}\n" +
                   $"Memory: {SystemInfo.systemMemorySize}MB\n" +
                   $"Processor: {SystemInfo.processorType} ({SystemInfo.processorCount} cores)\n" +
                   $"Graphics: {SystemInfo.graphicsDeviceName}\n" +
                   $"Graphics Memory: {SystemInfo.graphicsMemorySize}MB";
        }
    }
}
