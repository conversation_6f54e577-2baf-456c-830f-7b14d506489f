# 3D Space Tower Defense Game

A mobile tower defense game built for Unity with C#, targeting Android and iOS platforms.

## Project Structure

```
Assets/
├── Art/
├── Audio/
├── Prefabs/
├── Scenes/
├── Scripts/
│   ├── Core/
│   ├── Towers/
│   ├── Enemies/
│   ├── UI/
│   ├── Audio/
│   └── Data/
└── UI/
```

## Features

- 3D StarCraft-like visual style
- Mobile touch controls
- Wave-based enemy spawning
- Tower placement and upgrade system
- Multiple levels with progression
- Audio system with music and SFX
- Mobile-optimized UI

## Setup Instructions

1. Create a new Unity 3D project (URP recommended)
2. Copy the Scripts folder to your Assets directory
3. Set up the scenes (MainMenu, Level1)
4. Configure mobile build settings for Android/iOS
5. Import or create 3D models for towers and enemies

## Scripts Overview

- **GameManager**: Core game state management
- **WaveSpawner**: Enemy wave spawning system
- **Tower**: Tower behavior and shooting mechanics
- **Enemy**: Enemy AI and pathfinding
- **TouchInput**: Mobile input handling
- **UIManager**: Mobile UI management
- **AudioManager**: Audio system

## Development Notes

- Uses ScriptableObjects for data-driven design
- Optimized for mobile performance
- Modular architecture for easy expansion
- Touch-friendly UI design
